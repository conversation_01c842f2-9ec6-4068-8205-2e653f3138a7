# Master Project Brief Creation Task
## System-Level Initiative Planning for Microservices Architecture

## Purpose

- Transform high-level business requirements into comprehensive project briefs for system-wide initiatives
- Define clear project scope, vision, and strategic context for microservices ecosystems
- Provide foundation for Product Manager Master PRD development and cross-service coordination
- Establish framework for platform engineering and AI integration planning

Remember as you follow the upcoming instructions:

- Your project brief must address system-wide concerns and cross-cutting capabilities
- Output will be directly used by Product Manager for Master PRD generation and architectural planning
- Focus on strategic "what" and "why" while setting foundation for detailed "how"
- Ensure alignment with enterprise architecture principles and business strategy

## Instructions

### 1. Determine Project Brief Scope and Type

Before project brief generation, establish:

A. **Project Category:**
   - Platform Engineering Initiative (IDP, developer experience)
   - Business Capability Expansion (new product features across services)
   - Technology Modernization (architecture transformation)
   - AI Integration Project (agentic AI capabilities across ecosystem)
   - Compliance/Security Initiative (enterprise-wide requirements)

B. **System Impact Assessment:**
   - Number of services affected
   - Cross-cutting concerns involved
   - Platform infrastructure changes required
   - Team coordination complexity

### 2. Gather Project Context and Requirements

**Essential Information Collection:**
- Business problem statement and strategic context
- Stakeholder requirements and success criteria
- Technical constraints and architectural principles
- Timeline expectations and resource availability
- Integration requirements with existing systems

**Microservices-Specific Considerations:**
- Service boundary impacts and potential changes
- Cross-service communication requirements
- Data consistency and transaction patterns
- Event-driven architecture implications
- Platform engineering and operational requirements

### 3. Select Appropriate Template

**Template Selection Logic:**
- **System-Wide Projects:** Use `master-project-brief-tmpl` for comprehensive ecosystem initiatives
- **Platform Projects:** Use `master-project-brief-tmpl` with platform engineering focus
- **Legacy Projects:** Use `project-brief-tmpl` for backward compatibility when needed

### 4. Create Comprehensive Project Brief

**Core Sections to Address:**

A. **Executive Summary and Vision**
   - Strategic business context and problem statement
   - Value proposition and competitive advantage
   - High-level solution approach and architecture philosophy

B. **Scope and Objectives**
   - Clear project boundaries and deliverables
   - Success criteria and measurable outcomes
   - Timeline and milestone framework

C. **Microservices Architecture Context**
   - Service ecosystem impact assessment
   - Cross-cutting concerns and platform requirements
   - Integration patterns and communication strategies

D. **AI Integration Strategy** (if applicable)
   - Agentic AI capabilities and placement
   - Human-AI collaboration patterns
   - AI governance and ethics considerations

E. **Platform Engineering Requirements**
   - Developer experience improvements
   - Infrastructure and tooling needs
   - Operational excellence and SRE practices

F. **Implementation Framework**
   - Team topology and organizational alignment
   - Technology stack and architectural decisions
   - Risk assessment and mitigation strategies

### 5. Validate and Refine Brief

**Quality Assurance Checklist:**
- [ ] Clear problem statement and business justification
- [ ] Well-defined scope and success criteria
- [ ] Microservices architecture alignment
- [ ] Cross-service impact assessment
- [ ] Platform engineering considerations
- [ ] AI integration strategy (if applicable)
- [ ] Implementation feasibility and resource requirements
- [ ] Stakeholder alignment and communication plan

### 6. Prepare for PRD Development

**Handoff Preparation:**
- Ensure brief provides sufficient context for Product Manager Master PRD creation
- Include specific guidance for Product Manager agent on PRD development approach
- Identify key stakeholders and decision-making processes
- Establish framework for service-specific brief creation if needed

## Expected Outputs

### Primary Deliverable
Complete Master Project Brief following the `master-project-brief-tmpl` template with:
- Comprehensive project vision and strategic context
- Clear scope definition and success criteria
- Microservices architecture framework
- AI integration strategy and platform requirements
- Implementation roadmap and resource planning

### Secondary Deliverables
- Product Manager agent prompt for Master PRD development
- Framework for individual service brief creation
- Stakeholder communication and alignment plan
- Risk assessment and mitigation framework

## Success Criteria

**Brief Quality Standards:**
- Clear, actionable problem statement and solution vision
- Well-defined scope with measurable success criteria
- Comprehensive microservices architecture context
- Detailed platform engineering and AI integration requirements
- Feasible implementation plan with realistic timelines

**Stakeholder Alignment:**
- All key stakeholders understand and approve project vision
- Clear decision-making processes and communication channels
- Agreed-upon success criteria and measurement framework
- Established governance and oversight mechanisms

**Technical Foundation:**
- Solid architectural foundation for detailed design
- Clear integration patterns and communication strategies
- Platform engineering requirements well-defined
- AI integration strategy aligned with business objectives

## Next Steps

Upon completion of the project brief:

1. **Review and Approval:** Stakeholder review and formal approval process
2. **Master PRD Development:** Hand off to Product Manager agent for detailed requirements
3. **Architecture Planning:** Engage Platform Architect and Service Mesh Architect
4. **Team Coordination:** Establish cross-functional team structure and communication
5. **Service Brief Creation:** Create individual service briefs for affected services

This task ensures comprehensive project planning that aligns with BMAD Method 4.0 principles for enterprise-scale microservices development with integrated AI capabilities.
