Okay, I can help with that! Here are the available agents and the primary tasks they can help you with. Please tell me which agent and (optionally) which specific task you'd like to use.

1.  **Agent 'BMAD' (BMAD)** [cite: 1]
    * Description: For general BMAD Method or Agent queries, oversight, or advice and guidance when unsure. [cite: 1]
    * Tasks: General guidance, Orchestration of other agents.

2.  **Agent 'Analyst' (<PERSON>)**: "Project Analyst and Brainstorming Coach"[cite: 3].
    **Description**:  Good for initial project analysis and brainstorming. Project Analyst, Brainstorming Coach, and Brief Creation Specialist. Specializes in creating both system-level project briefs and individual service briefs for microservices architecture. [cite: 3]
    **Tasks**:
        * Brain Storming
        * Create Deep Research Prompt [cite: 4]
        * Create Project Brief [cite: 4]
        * Create Service Brief [cite: 4]

3.  **Agent 'Product Manager' (John)**: "For PRDs, project planning, PM checklists and potential replans." [cite: 5]
    * Description: For PRDs, project planning, PM checklists and potential replans. Specializes in transforming project briefs and service briefs into comprehensive PRDs for microservices architecture and navigating changes. [cite: 5]
    * Tasks:
        * Create Master Prd [cite: 5]
        * Create Service Prd [cite: 5]
        * Correct Course [cite: 5]
        * Create Deep Research Prompt [cite: 5]


4.  **Agent 'Architect' (Fred)**: For system architecture, technical design, architecture checklists. [cite: 6]
    **Description**: Handles system architecture and technical design. [cite: 6]
    **Tasks**:
        * Create Architecture [cite: 6]
        * Create Deep Research Prompt [cite: 6]

5.  **Agent 'Design Architect' (Jane)**: For UI/UX specifications, front-end architecture. [cite: 7]
    **Description**: Specializes in  UI/UX specifications, frontend architecture, design system governance, and microfrontend design coordination for enterprise applications. [cite: 7]
    **Tasks**:
        * Create Frontend Architecture [cite: 8]
        * Create Ai Frontend Prompt [cite: 8]
        * Create UX/UI Spec [cite: 8]
        * Microfrontend Decomposition Analysis [cite: 8]
        * Design System Integration Strategy [cite: 8]
6.  **Agent 'PO' (Sarah)**: "Product Owner" [cite: 9]
    **Description**:Manages the product backlog, validates documents, and helps with story creation and adjustments.. [cite: 9]
    **Tasks**:
        * Run Pm Checklist [from `checklists#pm-checklist` for Product Manager John, PO Sarah has `checklists#po-master-checklist`]
        * Run Po Master Checklist [cite: 9]
        * Run Change Checklist [cite: 9]
        * Extracts Epics and shards the Architecture [cite: 9]
        * Correct Course [cite: 9]

7.  **Agent 'SM' (Bob)** [cite: 10]
    * Description: A very Technical Scrum Master helps the team run the Scrum process. [cite: 10]
    * Tasks:
        * Run Change Checklist [cite: 10]
        * Run Story Dod Checklist [cite: 10]
        * Run Story Draft Checklist [cite: 10]
        * Correct Course [cite: 10]
        * Draft a story for dev agent [cite: 10]

8.  **Agent 'Service Mesh Architect' (Alex)** [cite: 11]
    * Description: Service mesh architecture, traffic management, distributed communication patterns for microservices ecosystems, and frontend service integration strategies. [cite: 11]
    * Tasks:
        * Create Service Integration Contract [cite: 12]
        * Service Decomposition Analysis [cite: 12]
        * Frontend Service Communication Design [cite: 12]
        * API Gateway Strategy Design [cite: 12]
        * Create Architecture [cite: 12]

9.  **Agent 'Platform Engineer' (Taylor)** [cite: 13]
    * Description: Internal Developer Platform design, developer experience optimization, and operational excellence for microservices platforms. [cite: 13]
    * Tasks:
        * Platform Engineering Strategy Design [cite: 13]
        * Create Architecture [cite: 13]
        * Run Platform Engineering Checklist [cite: 13]
        * Run Platform Architect Checklist [cite: 13]

10. **Agent 'AI Orchestration Specialist' (Morgan)** [cite: 14]
    * Description: Multi-agent AI system design, agentic AI orchestration, and human-AI collaboration frameworks for AI-native applications. [cite: 14]
    * Tasks:
        * AI Agent Orchestration Design [cite: 14]
        * Create Architecture [cite: 14]
        * Run AI Orchestration Checklist [cite: 14]
        * Run AI Integration Checklist [cite: 14]

11. **Agent 'Microfrontend Architect' (Jordan)** [cite: 15]
    * Description: Microfrontend architecture design, frontend service decomposition, and distributed UI system orchestration for scalable enterprise applications. [cite: 15]
    * Tasks:
        * Microfrontend Decomposition Analysis [cite: 16]
        * Frontend Service Communication Design [cite: 16]
        * Microfrontend Deployment Strategy [cite: 16]
        * Create Frontend Architecture [cite: 16]
        * Create Architecture [cite: 16]
12.  **SM (Bob)**: "A very Technical Scrum Master helps the team run the Scrum process." [cite: 7, 8]
    * **Description**: A technical Scrum Master to help run the Scrum process, including story drafting and course correction.
    * **Tasks**:
        * Run Change Checklist
        * Run Story Dod Checklist
        * Run Story Draft Checklist
        * Correct Course
        * Draft a story for dev agent

Which agent and task would you like to start with? The default interaction mode will be interactive, but you can also specify "YOLO" mode if you prefer.