# Individual Service Brief Creation Task
## Service-Specific Planning and Design for Microservices

## Purpose

- Transform service-level requirements into focused service briefs for individual microservices
- Define clear service scope, responsibilities, and integration patterns
- Provide foundation for Product Manager Service PRD development and implementation planning
- Ensure service alignment with system-wide architecture and business objectives

Remember as you follow the upcoming instructions:

- Your service brief must align with Master Project Brief and system-wide requirements
- Output will be directly used by Product Manager for Service PRD generation and development planning
- Focus on service-specific "what" and "why" while maintaining system coherence
- Ensure clear integration points and dependencies with other services

## Instructions

### 1. Determine Service Brief Scope and Context

Before service brief generation, establish:

A. **Service Type and Category:**
   - Core Business Service (domain logic and business rules)
   - Data Service (data management and persistence)
   - Integration Service (external system connectivity)
   - Platform Service (infrastructure and shared capabilities)
   - AI Agent Service (agentic AI capabilities and orchestration)

B. **Service Context Assessment:**
   - Relationship to Master Project Brief
   - Position in service ecosystem
   - Dependencies on other services
   - Integration complexity and patterns

### 2. Gather Service Requirements and Context

**Essential Information Collection:**
- Service business purpose and value proposition
- Functional requirements and capabilities
- Non-functional requirements (performance, security, scalability)
- Integration requirements with other services
- Data ownership and management responsibilities

**Microservices-Specific Considerations:**
- Service boundary definition and ownership
- Communication patterns (synchronous/asynchronous)
- Data consistency and transaction requirements
- Event publishing and consumption patterns
- Service discovery and registration needs

### 3. Select Appropriate Template

**Template Selection Logic:**
- **Individual Services:** Use `individual-service-brief-tmpl` for focused service planning
- **AI Services:** Use `individual-service-brief-tmpl` with AI-specific considerations
- **Platform Services:** Use `individual-service-brief-tmpl` with platform engineering focus

### 4. Create Comprehensive Service Brief

**Core Sections to Address:**

A. **Service Overview and Purpose**
   - Clear service name and identifier
   - Business purpose and value proposition
   - Service boundaries and responsibilities

B. **Functional Requirements**
   - Core business capabilities and features
   - API specifications and contracts
   - Data processing and business logic

C. **Integration and Communication**
   - Dependencies on other services
   - Communication patterns and protocols
   - Event publishing and consumption
   - External system integrations

D. **Data Management**
   - Data ownership and boundaries
   - Storage requirements and patterns
   - Data consistency and synchronization
   - Privacy and compliance considerations

E. **Non-Functional Requirements**
   - Performance and scalability targets
   - Security and authentication requirements
   - Availability and reliability expectations
   - Monitoring and observability needs

F. **AI Integration** (if applicable)
   - AI agent capabilities and placement
   - Machine learning model integration
   - Human-AI collaboration patterns
   - AI governance and ethics compliance

### 5. Define Service Architecture Context

**Technical Architecture Considerations:**
- Technology stack and framework selection
- Deployment and containerization strategy
- Configuration management and secrets
- Service mesh integration patterns

**Operational Requirements:**
- Monitoring and alerting specifications
- Logging and tracing requirements
- Health check and readiness probes
- Backup and disaster recovery procedures

### 6. Validate Service Brief Quality

**Quality Assurance Checklist:**
- [ ] Clear service purpose and business value
- [ ] Well-defined service boundaries and responsibilities
- [ ] Comprehensive functional requirements
- [ ] Clear integration patterns and dependencies
- [ ] Non-functional requirements specified
- [ ] Data ownership and management defined
- [ ] AI integration strategy (if applicable)
- [ ] Operational and monitoring requirements
- [ ] Alignment with system-wide architecture

### 7. Prepare for Service PRD Development

**Handoff Preparation:**
- Ensure brief provides sufficient context for Product Manager Service PRD creation
- Include specific guidance for Product Manager agent on PRD development approach
- Identify service-specific stakeholders and requirements
- Establish framework for technical design and implementation

## Expected Outputs

### Primary Deliverable
Complete Individual Service Brief following the `individual-service-brief-tmpl` template with:
- Clear service definition and business purpose
- Comprehensive functional and non-functional requirements
- Integration patterns and communication specifications
- Data management and ownership boundaries
- AI integration strategy and operational requirements

### Secondary Deliverables
- Product Manager agent prompt for Service PRD development
- Service integration contract specifications
- Technical architecture guidance and constraints
- Implementation planning framework

## Success Criteria

**Brief Quality Standards:**
- Clear, focused service definition with well-defined boundaries
- Comprehensive requirements covering all service aspects
- Clear integration patterns and dependency management
- Feasible implementation plan with realistic constraints
- Alignment with system-wide architecture and standards

**Service Alignment:**
- Service purpose aligns with business objectives
- Clear value proposition and ownership model
- Integration patterns support system-wide goals
- Non-functional requirements meet enterprise standards

**Technical Foundation:**
- Solid foundation for detailed technical design
- Clear API contracts and communication patterns
- Data management strategy well-defined
- Operational requirements comprehensive and measurable

## Next Steps

Upon completion of the service brief:

1. **Review and Validation:** Technical and business stakeholder review
2. **Service PRD Development:** Hand off to Product Manager agent for detailed requirements
3. **Technical Design:** Engage Service Mesh Architect for integration design
4. **Implementation Planning:** Establish development team and timeline
5. **Integration Coordination:** Coordinate with dependent services and teams

This task ensures focused service planning that aligns with BMAD Method 4.0 principles for enterprise-scale microservices development while maintaining clear service boundaries and responsibilities.
