# User-Defined Preferred Patterns and Preferences

See example files in this folder.
list out your technical preferences, patterns you like to follow, language framework or starter project preferences.

Anything you learn or prefer over time to drive future project choices, add the here.

## Microfrontend Architecture Preferences

### Frontend Technologies
- **Framework**: React 18+ with TypeScript for type safety and modern features
- **Build Tool**: Webpack 5 with Module Federation for microfrontend composition
- **Styling**: Tailwind CSS with design tokens for consistent styling across microfrontends
- **State Management**: Zustand for local state, TanStack Query for server state
- **Testing**: Jest + React Testing Library for unit tests, Playwright for E2E testing

### Microfrontend Patterns
- **Architecture**: Shell + Microfrontend pattern with runtime composition
- **Communication**: Event-driven communication with custom events and shared state
- **Routing**: Distributed routing with each microfrontend managing its own routes
- **Design System**: Centralized component library with NPM distribution
- **Deployment**: Independent CI/CD pipelines with container-based deployment

### Backend Integration
- **API Gateway**: Kong or NGINX for centralized API management
- **Authentication**: OAuth 2.0/OIDC with JWT tokens for stateless authentication
- **Communication**: RESTful APIs with GraphQL for complex data requirements
- **Caching**: Multi-layer caching with Redis and browser cache optimization
- **Monitoring**: Prometheus + Grafana for metrics, Jaeger for distributed tracing

### Development Workflow
- **Monorepo**: Nx or Lerna for managing multiple microfrontend packages
- **Code Quality**: ESLint + Prettier for code formatting, Husky for git hooks
- **Documentation**: Storybook for component documentation, OpenAPI for API docs
- **Performance**: Lighthouse CI for performance monitoring, Bundle analyzer for optimization
- **Security**: OWASP security practices, automated vulnerability scanning
