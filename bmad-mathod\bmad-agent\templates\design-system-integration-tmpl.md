# Design System Integration Template

## Document Information
- **Document Type**: Design System Integration Strategy
- **Version**: 1.0
- **Status**: [Draft/Review/Approved]
- **Author**: [Design Architect Name]
- **Date**: [Creation Date]
- **Last Updated**: [Last Update Date]

## Executive Summary

### Integration Overview
- **Project Name**: [Project Name]
- **Design System**: [Design System Name/Version]
- **Microfrontend Count**: [Number of microfrontends]
- **Technology Stack**: [React, Next.js, Tailwind CSS, etc.]
- **Integration Pattern**: [Centralized/Federated/Hybrid]

### Key Objectives
- **Consistency**: Unified user experience across all microfrontends
- **Efficiency**: Reduced design and development time
- **Maintainability**: Centralized design token and component management
- **Scalability**: Support for growing number of microfrontends
- **Accessibility**: WCAG 2.1 AA compliance across all components

## Design System Architecture

### Design Token Hierarchy
```
Global Tokens (Brand Level)
├── Colors
│   ├── Primary: #007bff
│   ├── Secondary: #6c757d
│   ├── Success: #28a745
│   ├── Warning: #ffc107
│   └── Error: #dc3545
├── Typography
│   ├── Font Family: 'Inter', sans-serif
│   ├── Font Sizes: 12px, 14px, 16px, 18px, 24px, 32px
│   ├── Font Weights: 400, 500, 600, 700
│   └── Line Heights: 1.2, 1.4, 1.6, 1.8
├── Spacing
│   ├── Base Unit: 4px
│   ├── Scale: 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px
│   └── Component Spacing: 8px, 16px, 24px
└── Breakpoints
    ├── Mobile: 320px
    ├── Tablet: 768px
    ├── Desktop: 1024px
    └── Large: 1440px

Semantic Tokens (Context Level)
├── Surface Colors
│   ├── Background: var(--color-neutral-50)
│   ├── Surface: var(--color-neutral-100)
│   └── Overlay: var(--color-neutral-900)
├── Text Colors
│   ├── Primary: var(--color-neutral-900)
│   ├── Secondary: var(--color-neutral-600)
│   └── Disabled: var(--color-neutral-400)
└── Interactive Colors
    ├── Link: var(--color-primary-600)
    ├── Link Hover: var(--color-primary-700)
    └── Focus: var(--color-primary-500)

Component Tokens (Implementation Level)
├── Button
│   ├── Primary Background: var(--color-primary-600)
│   ├── Primary Text: var(--color-neutral-50)
│   ├── Secondary Background: var(--color-neutral-100)
│   └── Secondary Text: var(--color-neutral-900)
├── Input
│   ├── Background: var(--color-neutral-50)
│   ├── Border: var(--color-neutral-300)
│   ├── Focus Border: var(--color-primary-500)
│   └── Error Border: var(--color-error-500)
└── Card
    ├── Background: var(--color-neutral-50)
    ├── Border: var(--color-neutral-200)
    └── Shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
```

### Component Library Structure
```
Design System Package
├── Tokens
│   ├── colors.json
│   ├── typography.json
│   ├── spacing.json
│   └── breakpoints.json
├── Components
│   ├── Primitives
│   │   ├── Button
│   │   ├── Input
│   │   ├── Text
│   │   └── Icon
│   ├── Composites
│   │   ├── Card
│   │   ├── Modal
│   │   ├── Navigation
│   │   └── Form
│   └── Layouts
│       ├── Container
│       ├── Grid
│       ├── Stack
│       └── Flex
├── Themes
│   ├── light.json
│   ├── dark.json
│   └── high-contrast.json
└── Documentation
    ├── Storybook
    ├── Usage Guidelines
    └── Migration Guides
```

## Integration Strategies

### Centralized Distribution
```typescript
// Design System Package Structure
interface DesignSystemPackage {
  tokens: DesignTokens;
  components: ComponentLibrary;
  themes: ThemeConfiguration;
  utilities: UtilityFunctions;
  documentation: Documentation;
}

// Package.json for design system
{
  "name": "@company/design-system",
  "version": "2.1.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "exports": {
    ".": "./dist/index.js",
    "./tokens": "./dist/tokens/index.js",
    "./components": "./dist/components/index.js",
    "./themes": "./dist/themes/index.js"
  },
  "peerDependencies": {
    "react": ">=18.0.0",
    "react-dom": ">=18.0.0"
  }
}
```

### Microfrontend Integration
```typescript
// Microfrontend integration example
import { 
  DesignSystemProvider,
  Button,
  Card,
  useTheme,
  tokens 
} from '@company/design-system';

// App-level integration
function App() {
  return (
    <DesignSystemProvider theme="light">
      <MicrofrontendContent />
    </DesignSystemProvider>
  );
}

// Component usage
function MicrofrontendContent() {
  const theme = useTheme();
  
  return (
    <Card>
      <Button variant="primary" size="medium">
        Action Button
      </Button>
    </Card>
  );
}
```

### Token Integration with Tailwind CSS
```javascript
// tailwind.config.js
const designTokens = require('@company/design-system/tokens');

module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: designTokens.colors,
      fontFamily: designTokens.typography.fontFamily,
      fontSize: designTokens.typography.fontSize,
      spacing: designTokens.spacing,
      screens: designTokens.breakpoints,
    },
  },
  plugins: [
    require('@company/design-system/tailwind-plugin'),
  ],
};
```

## Component Development Standards

### Component API Design
```typescript
// Standard component interface
interface ComponentProps {
  // Visual variants
  variant?: 'primary' | 'secondary' | 'tertiary';
  size?: 'small' | 'medium' | 'large';
  
  // State props
  disabled?: boolean;
  loading?: boolean;
  error?: boolean;
  
  // Accessibility props
  'aria-label'?: string;
  'aria-describedby'?: string;
  id?: string;
  
  // Event handlers
  onClick?: (event: MouseEvent) => void;
  onFocus?: (event: FocusEvent) => void;
  onBlur?: (event: FocusEvent) => void;
  
  // Styling props
  className?: string;
  style?: CSSProperties;
  
  // Content
  children?: ReactNode;
}

// Example Button component
interface ButtonProps extends ComponentProps {
  type?: 'button' | 'submit' | 'reset';
  href?: string; // For link buttons
  target?: string;
  rel?: string;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'primary', size = 'medium', ...props }, ref) => {
    const classes = cn(
      'btn',
      `btn--${variant}`,
      `btn--${size}`,
      props.disabled && 'btn--disabled',
      props.loading && 'btn--loading',
      props.className
    );
    
    return (
      <button
        ref={ref}
        className={classes}
        {...props}
      >
        {props.loading && <Spinner size="small" />}
        {props.children}
      </button>
    );
  }
);
```

### Accessibility Standards
```typescript
// Accessibility utilities
interface A11yProps {
  role?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  'aria-live'?: 'polite' | 'assertive' | 'off';
  tabIndex?: number;
}

// Focus management hook
const useFocusManagement = () => {
  const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
  
  const trapFocus = (container: HTMLElement) => {
    const focusable = container.querySelectorAll(focusableElements);
    const firstFocusable = focusable[0] as HTMLElement;
    const lastFocusable = focusable[focusable.length - 1] as HTMLElement;
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstFocusable) {
            lastFocusable.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastFocusable) {
            firstFocusable.focus();
            e.preventDefault();
          }
        }
      }
    };
    
    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  };
  
  return { trapFocus };
};
```

## Theme Management

### Theme Configuration
```typescript
// Theme interface
interface Theme {
  name: string;
  colors: ColorPalette;
  typography: TypographyScale;
  spacing: SpacingScale;
  shadows: ShadowScale;
  borderRadius: BorderRadiusScale;
  transitions: TransitionScale;
}

// Light theme
const lightTheme: Theme = {
  name: 'light',
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      500: '#3b82f6',
      600: '#2563eb',
      900: '#1e3a8a',
    },
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      500: '#6b7280',
      900: '#111827',
    },
  },
  // ... other theme properties
};

// Dark theme
const darkTheme: Theme = {
  name: 'dark',
  colors: {
    primary: {
      50: '#1e3a8a',
      100: '#2563eb',
      500: '#3b82f6',
      600: '#dbeafe',
      900: '#eff6ff',
    },
    neutral: {
      50: '#111827',
      100: '#1f2937',
      500: '#9ca3af',
      900: '#f9fafb',
    },
  },
  // ... other theme properties
};
```

### Theme Provider Implementation
```typescript
// Theme context
const ThemeContext = createContext<{
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}>({
  theme: lightTheme,
  setTheme: () => {},
  toggleTheme: () => {},
});

// Theme provider component
export const DesignSystemProvider: React.FC<{
  children: ReactNode;
  defaultTheme?: Theme;
}> = ({ children, defaultTheme = lightTheme }) => {
  const [theme, setTheme] = useState(defaultTheme);
  
  const toggleTheme = useCallback(() => {
    setTheme(current => current.name === 'light' ? darkTheme : lightTheme);
  }, []);
  
  // Apply CSS custom properties
  useEffect(() => {
    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
      if (typeof value === 'object') {
        Object.entries(value).forEach(([shade, color]) => {
          root.style.setProperty(`--color-${key}-${shade}`, color);
        });
      } else {
        root.style.setProperty(`--color-${key}`, value);
      }
    });
  }, [theme]);
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Theme hook
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a DesignSystemProvider');
  }
  return context;
};
```

## Responsive Design Strategy

### Breakpoint Management
```typescript
// Responsive utilities
const breakpoints = {
  mobile: '320px',
  tablet: '768px',
  desktop: '1024px',
  large: '1440px',
} as const;

// Media query hook
const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);
  
  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    
    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);
  
  return matches;
};

// Responsive component example
const ResponsiveGrid: React.FC<{
  children: ReactNode;
  columns?: { mobile?: number; tablet?: number; desktop?: number };
}> = ({ children, columns = { mobile: 1, tablet: 2, desktop: 3 } }) => {
  const isMobile = useMediaQuery(`(max-width: ${breakpoints.tablet})`);
  const isTablet = useMediaQuery(`(min-width: ${breakpoints.tablet}) and (max-width: ${breakpoints.desktop})`);
  
  const currentColumns = isMobile 
    ? columns.mobile 
    : isTablet 
    ? columns.tablet 
    : columns.desktop;
  
  return (
    <div 
      className="grid gap-4"
      style={{ gridTemplateColumns: `repeat(${currentColumns}, 1fr)` }}
    >
      {children}
    </div>
  );
};
```

## Testing Strategy

### Component Testing
```typescript
// Component test example
import { render, screen, fireEvent } from '@testing-library/react';
import { DesignSystemProvider } from '@company/design-system';
import { Button } from './Button';

const renderWithProvider = (component: ReactElement) => {
  return render(
    <DesignSystemProvider>
      {component}
    </DesignSystemProvider>
  );
};

describe('Button Component', () => {
  it('renders with correct variant styles', () => {
    renderWithProvider(<Button variant="primary">Click me</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('btn--primary');
  });
  
  it('handles click events', () => {
    const handleClick = jest.fn();
    renderWithProvider(
      <Button onClick={handleClick}>Click me</Button>
    );
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('is accessible', async () => {
    renderWithProvider(<Button aria-label="Submit form">Submit</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveAccessibleName('Submit form');
  });
});
```

### Visual Regression Testing
```typescript
// Storybook stories for visual testing
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'tertiary'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Button',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="flex gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="tertiary">Tertiary</Button>
    </div>
  ),
};
```

## Documentation Strategy

### Component Documentation
```typescript
// Component documentation template
/**
 * Button component for user interactions
 * 
 * @example
 * ```tsx
 * <Button variant="primary" size="medium" onClick={handleClick}>
 *   Click me
 * </Button>
 * ```
 */
export interface ButtonProps {
  /** Visual style variant */
  variant?: 'primary' | 'secondary' | 'tertiary';
  /** Size of the button */
  size?: 'small' | 'medium' | 'large';
  /** Disabled state */
  disabled?: boolean;
  /** Loading state with spinner */
  loading?: boolean;
  /** Click event handler */
  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
  /** Button content */
  children: ReactNode;
}
```

### Usage Guidelines
```markdown
## Button Usage Guidelines

### When to Use
- Primary actions (save, submit, continue)
- Secondary actions (cancel, back)
- Tertiary actions (learn more, view details)

### When Not to Use
- Navigation between pages (use Link instead)
- Toggling states (use Toggle or Switch)
- Multiple selection (use Checkbox)

### Accessibility
- Always provide meaningful text or aria-label
- Use appropriate button type (button, submit, reset)
- Ensure sufficient color contrast (4.5:1 minimum)
- Provide focus indicators
- Support keyboard navigation

### Best Practices
- Use clear, action-oriented labels
- Limit to one primary button per section
- Group related actions together
- Consider loading states for async actions
```

## Migration Strategy

### Version Management
```json
{
  "migration_guide": {
    "from_version": "1.x",
    "to_version": "2.x",
    "breaking_changes": [
      {
        "component": "Button",
        "change": "Renamed 'type' prop to 'variant'",
        "migration": "Replace 'type' with 'variant' in all Button components"
      }
    ],
    "new_features": [
      {
        "component": "Card",
        "feature": "Added elevation prop for shadow variants"
      }
    ],
    "evolved_features": [
      {
        "component": "EnhancedButton",
        "evolution_date": "2024-06-01",
        "enhancement_date": "2024-12-01",
        "enhancement": "Button"
      }
    ]
  }
}
```

### Automated Migration Tools
```typescript
// Codemod for automated migration
import { Transform } from 'jscodeshift';

const transform: Transform = (fileInfo, api) => {
  const j = api.jscodeshift;
  const root = j(fileInfo.source);
  
  // Migrate Button type prop to variant
  root
    .find(j.JSXElement, {
      openingElement: {
        name: { name: 'Button' }
      }
    })
    .find(j.JSXAttribute, {
      name: { name: 'type' }
    })
    .forEach(path => {
      path.node.name.name = 'variant';
    });
  
  return root.toSource();
};

export default transform;
```

## Governance and Maintenance

### Design System Governance
- **Design Review Board**: Cross-functional team for design decisions
- **Component Ownership**: Clear ownership and maintenance responsibilities
- **Contribution Process**: Guidelines for proposing new components
- **Breaking Change Policy**: Process for managing breaking changes
- **Release Schedule**: Regular release cadence and versioning strategy

### Quality Assurance
- **Automated Testing**: Unit, integration, and visual regression tests
- **Accessibility Audits**: Regular WCAG compliance validation
- **Performance Monitoring**: Bundle size and runtime performance tracking
- **Usage Analytics**: Component adoption and usage patterns
- **Feedback Collection**: User feedback and improvement suggestions

---

*This template provides a comprehensive framework for design system integration in microfrontend architectures. Customize based on your specific design system and organizational requirements.*
