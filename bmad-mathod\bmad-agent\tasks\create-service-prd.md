# Individual Service PRD Generation Task
## Detailed Service Requirements and Technical Specifications

## Purpose

- Transform Service Brief (created by <PERSON><PERSON><PERSON>) into detailed service-specific product requirements
- Define comprehensive technical specifications for individual microservices
- Provide foundation for service development teams and technical implementation
- Ensure service aligns with system-wide requirements and architectural principles

Remember as you follow the upcoming instructions:

- Your service PRD must align with the Master Project PRD and system-wide requirements
- Output will be directly used by development teams for service implementation
- Focus on service-specific "what" and "how" while maintaining system coherence
- Ensure clear integration points and dependencies with other services
- You are transforming strategic service briefs from Analyst into actionable service requirements

## Instructions

### 1. Define Service Context and Scope

Before Service PRD generation, establish:

A. **Service Type and Category:**
   - Core Business Service
   - Data Service
   - Integration Service
   - Platform Service
   - AI Agent Service

B. **Service Boundaries and Responsibilities:**
   - Clear definition of service ownership
   - Business capability alignment
   - Data ownership boundaries
   - Integration responsibilities

C. **System Integration Context:**
   - Dependencies on other services
   - Services that depend on this service
   - Cross-cutting concerns and shared requirements

### 2. Review Service-Level Inputs

Review all available inputs:
- Individual Service Brief (created by <PERSON>ly<PERSON>)
- Master Project PRD (system-wide requirements)
- Service catalog and dependency matrix
- Platform requirements and constraints
- AI integration requirements (if applicable)

### 3. Determine Service Complexity and Approach

Assess service complexity and choose appropriate approach:

A. **Simple Service Approach:** Straightforward CRUD operations with minimal business logic
B. **Complex Business Service:** Rich domain logic with multiple integration points
C. **AI-Enhanced Service:** Service with integrated AI agent capabilities
D. **Platform Service:** Infrastructure or cross-cutting service for other services

### 4. Process Service PRD Sections

Work through the Service PRD sections using the `individual-service-prd-tmpl` template:

#### 4A. Service Definition and Business Context
- **Service Mission and Purpose**: Clear business capability and value proposition
- **Business Domain Alignment**: Bounded context and domain model integration
- **Service Boundaries**: Responsibilities, ownership, and scope limitations
- **Stakeholders and Users**: Primary consumers and stakeholders

#### 4B. Functional Requirements
- **Core Capabilities**: Primary service functions and business logic
- **API Specifications**: Detailed endpoint specifications with schemas
- **Data Operations**: CRUD operations, queries, and data processing
- **Business Logic**: Algorithms, calculations, and decision processes

#### 4C. Non-Functional Requirements
- **Performance Requirements**: Latency, throughput, and response time targets
- **Scalability Requirements**: Horizontal scaling and load handling capacity
- **Reliability Requirements**: Uptime, fault tolerance, and error recovery
- **Security Requirements**: Authentication, authorization, and data protection

### 5. API Design and Contract Specifications

#### 5A. RESTful API Design
Create comprehensive API specifications including:
- **Endpoint Definitions**: Complete REST endpoints with HTTP methods
- **Request/Response Schemas**: JSON schemas with validation rules
- **Error Handling**: Error codes, messages, and recovery procedures
- **API Versioning**: Evolution strategy and backward compatibility

#### 5B. Integration Contracts
Define integration patterns:
- **Synchronous Communication**: Direct API calls and request-response patterns
- **Asynchronous Communication**: Event publishing and consumption patterns
- **External Integrations**: Third-party APIs and legacy system connections
- **Service Discovery**: Registration and discovery mechanisms

### 6. Data Model and Storage Design

#### 6A. Data Architecture
- **Entity Definitions**: Data models, relationships, and constraints
- **Database Schema**: Table structures, indexes, and optimization
- **Data Validation**: Input validation, business rules, and integrity checks
- **Data Lifecycle**: Creation, updates, archival, and deletion policies

#### 6B. Data Integration
- **Data Ownership**: Service-specific data boundaries and responsibilities
- **Data Sharing**: Cross-service data access patterns and protocols
- **Data Consistency**: Consistency models and transaction boundaries
- **Data Migration**: Migration strategies and data evolution

### 7. AI Integration (if applicable)

#### 7A. AI Agent Capabilities
- **Agent Integration Points**: Where and how AI agents interact with the service
- **AI Workflows**: Automated processes and decision-making capabilities
- **Model Integration**: AI model usage, inference, and scaling requirements
- **Human-AI Collaboration**: Handoff procedures and escalation protocols

#### 7B. AI Infrastructure Requirements
- **Vector Database Integration**: Embedding storage and semantic search
- **Model Serving**: Inference endpoints and scaling requirements
- **Context Management**: Memory and state preservation across interactions
- **AI Observability**: Monitoring, evaluation, and performance tracking

### 8. Security and Compliance

#### 8A. Security Architecture
- **Authentication**: Service-to-service and user authentication mechanisms
- **Authorization**: Role-based access control and permission management
- **Data Protection**: Encryption, privacy, and data handling procedures
- **Security Monitoring**: Threat detection and incident response

#### 8B. Compliance Requirements
- **Regulatory Compliance**: Industry-specific and regulatory requirements
- **Audit Trail**: Logging, tracking, and compliance reporting
- **Data Privacy**: Privacy-by-design and data minimization principles
- **Quality Assurance**: Validation procedures and quality controls

### 9. Testing and Quality Assurance

#### 9A. Testing Strategy
- **Unit Testing**: Component testing approach and coverage requirements
- **Integration Testing**: Service interaction and contract validation
- **Performance Testing**: Load testing, stress testing, and optimization
- **Security Testing**: Vulnerability assessment and penetration testing

#### 9B. Quality Gates
- **Code Quality**: Standards, reviews, and automated validation
- **Performance Validation**: Response time and throughput verification
- **Security Validation**: Security controls and vulnerability assessment
- **Compliance Validation**: Regulatory requirements and audit readiness

### 10. Deployment and Operations

#### 10A. Deployment Strategy
- **Containerization**: Docker configuration and image management
- **Orchestration**: Kubernetes deployment and service configuration
- **CI/CD Pipeline**: Build, test, and deployment automation
- **Environment Management**: Development, staging, and production configuration

#### 10B. Operational Requirements
- **Monitoring and Observability**: Health checks, metrics, and alerting
- **Scaling and Performance**: Auto-scaling policies and resource management
- **Maintenance and Updates**: Update procedures and maintenance windows
- **Incident Response**: Error handling, recovery, and escalation procedures

### 11. Service Dependencies and Integration

#### 11A. Dependency Management
- **Service Dependencies**: Required upstream services and external APIs
- **Downstream Consumers**: Services and applications that depend on this service
- **Dependency Coordination**: Version management and compatibility requirements
- **Fallback Strategies**: Graceful degradation and error handling

#### 11B. Integration Patterns
- **Event-Driven Integration**: Event publishing and consumption patterns
- **API Integration**: Synchronous service-to-service communication
- **Data Integration**: Shared data access and consistency patterns
- **External Integration**: Third-party services and legacy system connectivity

### 12. Performance and Scalability

#### 12A. Performance Requirements
- **Response Time Targets**: Latency requirements for different operations
- **Throughput Requirements**: Request handling capacity and load expectations
- **Resource Utilization**: CPU, memory, and storage optimization
- **Performance Monitoring**: Metrics collection and performance tracking

#### 12B. Scalability Strategy
- **Horizontal Scaling**: Auto-scaling policies and scaling triggers
- **Vertical Scaling**: Resource allocation and capacity planning
- **Load Distribution**: Load balancing and traffic management
- **Capacity Planning**: Growth projections and resource allocation

### 13. Documentation and Knowledge Management

#### 13A. Technical Documentation
- **API Documentation**: Comprehensive endpoint documentation and examples
- **Architecture Documentation**: Service design and technical specifications
- **Operational Documentation**: Runbooks and troubleshooting guides
- **Development Documentation**: Setup, configuration, and development guides

#### 13B. Knowledge Transfer
- **Team Onboarding**: New team member onboarding materials
- **Knowledge Sharing**: Documentation standards and knowledge base
- **Training Materials**: Service-specific training and education resources
- **Support Documentation**: User guides and support procedures

### 14. Validation and Review

#### 14A. Service Coherence Validation
- **System Alignment**: Ensure service aligns with system-wide requirements
- **Integration Completeness**: Verify all integration points are addressed
- **Quality Completeness**: Ensure all quality requirements are covered
- **Operational Readiness**: Validate deployment and operational procedures

#### 14B. Stakeholder Review
- **Technical Review**: Architecture and implementation feasibility
- **Business Review**: Business value and requirement alignment
- **Operational Review**: Deployment and maintenance feasibility
- **Security Review**: Security controls and compliance validation

## Deliverables

### Primary Deliverable
Complete Individual Service PRD following the `individual-service-prd-tmpl` template with:
- Comprehensive service-specific requirements
- Detailed API specifications and contracts
- Data model and storage requirements
- Integration patterns and dependencies
- Testing and quality assurance procedures
- Deployment and operational specifications

### Secondary Deliverables
- Service integration specifications
- API contract documentation
- Testing strategy and procedures
- Deployment and operational guides

## Success Criteria

- Service PRD provides clear and comprehensive service specifications
- All functional and non-functional requirements are properly defined
- API contracts are complete and well-documented
- Integration points and dependencies are clearly specified
- Testing strategy ensures service quality and reliability
- Deployment and operational procedures are well-defined
- Service aligns with system-wide architecture and requirements
