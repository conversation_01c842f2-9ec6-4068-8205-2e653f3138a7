name: BMAD Method CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18.19.0'
  BMAD_VERSION: '4.0.0'

jobs:
  validate:
    name: Validate Configuration
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Validate configuration
      run: npm run validate:config
      
    - name: Validate agent configuration
      run: npm run validate:agents

  build:
    name: Build Web Agent Bundle
    runs-on: ubuntu-latest
    needs: validate
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build web agent bundle
      run: npm run build
      
    - name: Validate build output
      run: npm run validate:build
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: bmad-web-agent-bundle
        path: build/
        retention-days: 30

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: validate
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level moderate
      
    - name: Check for vulnerabilities
      run: npm audit --audit-level high --production

  deploy:
    name: Deploy Web Agent Bundle
    runs-on: ubuntu-latest
    needs: [build, test, security]
    if: github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build for production
      run: |
        export NODE_ENV=production
        npm run deploy:web
        
    - name: Create deployment package
      run: |
        mkdir -p deployment
        cp -r build/* deployment/
        cp README.md deployment/
        cp docs/instruction.md deployment/
        
    - name: Upload deployment artifacts
      uses: actions/upload-artifact@v4
      with:
        name: bmad-deployment-${{ github.event.release.tag_name }}
        path: deployment/
        
    - name: Create release assets
      run: |
        cd deployment
        tar -czf ../bmad-web-agent-${{ github.event.release.tag_name }}.tar.gz *
        
    - name: Upload release assets
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: bmad-web-agent-${{ github.event.release.tag_name }}.tar.gz
        asset_name: bmad-web-agent-${{ github.event.release.tag_name }}.tar.gz
        asset_content_type: application/gzip

  quality:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linting
      run: npm run lint
      continue-on-error: true
      
    - name: Check code formatting
      run: npm run format
      continue-on-error: true
