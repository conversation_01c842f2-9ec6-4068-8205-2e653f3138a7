#!/usr/bin/env node
/**
 * BMAD Agent Configuration Validation Script
 * Validates agent configurations, file references, and path mappings
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  agentRoot: './bmad-agent',
  webConfigFile: './bmad-agent/web-bmad-orchestrator-agent.cfg.md',
  ideConfigFile: './bmad-agent/ide-bmad-orchestrator.cfg.md',
  checklistMappings: './bmad-agent/tasks/checklist-mappings.yml'
};

// Validation results
let validationResults = {
  errors: [],
  warnings: [],
  info: []
};

function log(level, message) {
  validationResults[level].push(message);
  const prefix = level === 'errors' ? '❌' : level === 'warnings' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} ${message}`);
}

function validateFileExists(filePath, description) {
  if (!fs.existsSync(filePath)) {
    log('errors', `Missing ${description}: ${filePath}`);
    return false;
  }
  return true;
}

function validateDirectoryStructure() {
  console.log('📋 Validating directory structure...');
  
  const requiredDirs = [
    'bmad-agent/personas',
    'bmad-agent/tasks', 
    'bmad-agent/templates',
    'bmad-agent/checklists',
    'bmad-agent/data'
  ];
  
  requiredDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      log('errors', `Missing required directory: ${dir}`);
    } else {
      log('info', `Directory exists: ${dir}`);
    }
  });
}

function parseWebAgentConfig() {
  console.log('📋 Validating web agent configuration...');
  
  if (!validateFileExists(CONFIG.webConfigFile, 'Web agent configuration')) {
    return null;
  }
  
  const content = fs.readFileSync(CONFIG.webConfigFile, 'utf8');
  const agents = [];
  
  // Parse agent definitions
  const agentMatches = content.match(/## Title: (.+?)(?=## Title:|$)/gs);
  if (agentMatches) {
    agentMatches.forEach(match => {
      const titleMatch = match.match(/## Title: (.+)/);
      const personaMatch = match.match(/- Persona: "(.+?)"/);
      
      if (titleMatch && personaMatch) {
        agents.push({
          title: titleMatch[1].trim(),
          persona: personaMatch[1].trim()
        });
      }
    });
  }
  
  log('info', `Found ${agents.length} agents in web configuration`);
  return agents;
}

function validatePersonaFiles(agents) {
  console.log('📋 Validating persona files...');
  
  if (!agents) return;
  
  agents.forEach(agent => {
    // Parse persona reference (e.g., "personas#bmad" -> "bmad.md")
    const personaRef = agent.persona.replace('personas#', '');
    const personaFile = path.join(CONFIG.agentRoot, 'personas', `${personaRef}.md`);
    
    if (!validateFileExists(personaFile, `Persona file for ${agent.title}`)) {
      log('errors', `Agent "${agent.title}" references missing persona: ${personaFile}`);
    } else {
      log('info', `Persona file exists for ${agent.title}: ${personaFile}`);
    }
  });
}

function validateTaskFiles() {
  console.log('📋 Validating task files...');
  
  const tasksDir = path.join(CONFIG.agentRoot, 'tasks');
  if (!fs.existsSync(tasksDir)) {
    log('errors', `Tasks directory not found: ${tasksDir}`);
    return;
  }
  
  const taskFiles = fs.readdirSync(tasksDir).filter(file => file.endsWith('.md'));
  log('info', `Found ${taskFiles.length} task files`);
  
  // Check for commonly referenced tasks
  const commonTasks = [
    'create-prd.md',
    'create-architecture.md', 
    'checklist-run-task.md',
    'correct-course.md'
  ];
  
  commonTasks.forEach(task => {
    const taskPath = path.join(tasksDir, task);
    if (!fs.existsSync(taskPath)) {
      log('warnings', `Common task file missing: ${task}`);
    }
  });
}

function validateChecklistMappings() {
  console.log('📋 Validating checklist mappings...');
  
  if (!validateFileExists(CONFIG.checklistMappings, 'Checklist mappings')) {
    return;
  }
  
  const content = fs.readFileSync(CONFIG.checklistMappings, 'utf8');
  
  // Check for path inconsistencies
  if (content.includes('docs/checklists/')) {
    log('warnings', 'Checklist mappings reference docs/checklists/ but checklists are in bmad-agent/checklists/');
  }
}

function validateBuildConfiguration() {
  console.log('📋 Validating build configuration...');
  
  const buildConfig = './build-web-agent.cfg.js';
  if (!validateFileExists(buildConfig, 'Build configuration')) {
    return;
  }
  
  try {
    const config = require(path.resolve(buildConfig));
    
    // Validate required fields
    const requiredFields = ['orchestrator_agent_prompt', 'agent_cfg', 'asset_root', 'build_dir'];
    requiredFields.forEach(field => {
      if (!config[field]) {
        log('errors', `Missing required build config field: ${field}`);
      }
    });
    
    // Validate referenced files exist
    if (config.orchestrator_agent_prompt && !fs.existsSync(config.orchestrator_agent_prompt)) {
      log('errors', `Build config references missing orchestrator prompt: ${config.orchestrator_agent_prompt}`);
    }
    
    if (config.agent_cfg && !fs.existsSync(config.agent_cfg)) {
      log('errors', `Build config references missing agent config: ${config.agent_cfg}`);
    }
    
  } catch (error) {
    log('errors', `Failed to parse build configuration: ${error.message}`);
  }
}

function validateMissingModernAgents() {
  console.log('📋 Checking for modern microservices agents...');

  const modernAgents = [
    'service-mesh-architect.md',
    'platform-engineer.md',
    'ai-orchestration-specialist.md',
    'platform-architect.md',
    'microfrontend-architect.md'
  ];

  const personasDir = path.join(CONFIG.agentRoot, 'personas');

  modernAgents.forEach(agent => {
    const agentPath = path.join(personasDir, agent);
    if (fs.existsSync(agentPath)) {
      log('info', `Modern agent exists: ${agent}`);
    } else {
      log('warnings', `Modern agent missing: ${agent} (needed for BMAD 4.0 microservices capabilities)`);
    }
  });
}

function validateMicrofrontendAssets() {
  console.log('📋 Checking for microfrontend-specific assets...');

  const microfrontendTemplates = [
    'microfrontend-architecture-tmpl.md',
    'frontend-service-integration-contract-tmpl.md',
    'microfrontend-deployment-strategy-tmpl.md',
    'design-system-integration-tmpl.md',
    'api-gateway-configuration-tmpl.md'
  ];

  const templatesDir = path.join(CONFIG.agentRoot, 'templates');

  microfrontendTemplates.forEach(template => {
    const templatePath = path.join(templatesDir, template);
    if (fs.existsSync(templatePath)) {
      log('info', `Microfrontend template exists: ${template}`);
    } else {
      log('warnings', `Microfrontend template missing: ${template} (needed for microfrontend architecture)`);
    }
  });

  const microfrontendTasks = [
    'microfrontend-decomposition-analysis.md',
    'frontend-service-communication-design.md',
    'microfrontend-deployment-strategy.md',
    'design-system-integration-strategy.md',
    'api-gateway-strategy-design.md'
  ];

  const tasksDir = path.join(CONFIG.agentRoot, 'tasks');

  microfrontendTasks.forEach(task => {
    const taskPath = path.join(tasksDir, task);
    if (fs.existsSync(taskPath)) {
      log('info', `Microfrontend task exists: ${task}`);
    } else {
      log('warnings', `Microfrontend task missing: ${task} (needed for microfrontend workflows)`);
    }
  });

  const microfrontendChecklists = [
    'microfrontend-architecture-checklist.md',
    'frontend-service-integration-checklist.md'
  ];

  const checklistsDir = path.join(CONFIG.agentRoot, 'checklists');

  microfrontendChecklists.forEach(checklist => {
    const checklistPath = path.join(checklistsDir, checklist);
    if (fs.existsSync(checklistPath)) {
      log('info', `Microfrontend checklist exists: ${checklist}`);
    } else {
      log('warnings', `Microfrontend checklist missing: ${checklist} (needed for microfrontend validation)`);
    }
  });
}

function generateReport() {
  console.log('\n📊 Validation Report');
  console.log('===================');
  
  console.log(`\n✅ Info: ${validationResults.info.length}`);
  console.log(`⚠️  Warnings: ${validationResults.warnings.length}`);
  console.log(`❌ Errors: ${validationResults.errors.length}`);
  
  if (validationResults.errors.length > 0) {
    console.log('\n❌ Critical Issues:');
    validationResults.errors.forEach(error => console.log(`   ${error}`));
  }
  
  if (validationResults.warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    validationResults.warnings.forEach(warning => console.log(`   ${warning}`));
  }
  
  const exitCode = validationResults.errors.length > 0 ? 1 : 0;
  console.log(`\n${exitCode === 0 ? '✅' : '❌'} Validation ${exitCode === 0 ? 'passed' : 'failed'}`);
  
  return exitCode;
}

// Main validation function
function main() {
  console.log('🔍 BMAD Agent Configuration Validator');
  console.log('=====================================\n');

  validateDirectoryStructure();
  const agents = parseWebAgentConfig();
  validatePersonaFiles(agents);
  validateTaskFiles();
  validateChecklistMappings();
  validateBuildConfiguration();
  validateMissingModernAgents();
  validateMicrofrontendAssets();

  const exitCode = generateReport();
  process.exit(exitCode);
}

// Run validation
if (require.main === module) {
  main();
}

module.exports = { main, validationResults };
