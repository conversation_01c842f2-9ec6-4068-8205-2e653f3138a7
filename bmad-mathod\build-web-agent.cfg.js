// build-web-agent.cfg.js
// This file contains the configuration for the build-web-agent.js script.

// Load environment variables if .env file exists
require('dotenv').config({ silent: true });

module.exports = {
  // Core build configuration
  orchestrator_agent_prompt: process.env.BMAD_ORCHESTRATOR_PROMPT || "./bmad-agent/web-bmad-orchestrator-agent.md",
  agent_cfg: process.env.BMAD_AGENT_CONFIG || "./bmad-agent/web-bmad-orchestrator-agent.cfg.md",
  asset_root: process.env.BMAD_ASSET_ROOT || "./bmad-agent/",
  build_dir: process.env.BMAD_BUILD_DIR || "./build/",

  // Build options
  validate_references: process.env.BMAD_VALIDATE_REFERENCES === 'true' || true,
  skip_ide_files: process.env.BMAD_SKIP_IDE_FILES === 'true' || true,
  strict_mode: process.env.BMAD_STRICT_MODE === 'true' || false,

  // Environment settings
  environment: process.env.NODE_ENV || 'development',
  version: process.env.BMAD_VERSION || '4.0.0',

  // Optional build optimizations
  minify_output: process.env.BMAD_MINIFY_OUTPUT === 'true' || false,
  include_metadata: process.env.BMAD_INCLUDE_METADATA !== 'false',
  bundle_size_limit: process.env.BMAD_BUNDLE_SIZE_LIMIT || '10MB',

  // Logging configuration
  log_level: process.env.BMAD_LOG_LEVEL || 'info',
  log_file: process.env.BMAD_LOG_FILE || null,

  // Security settings
  secure_mode: process.env.BMAD_SECURE_MODE === 'true' || false,
  validate_checksums: process.env.BMAD_VALIDATE_CHECKSUMS === 'true' || false,

  // Microfrontend-specific settings
  include_microfrontend_assets: process.env.BMAD_INCLUDE_MICROFRONTEND_ASSETS !== 'false',
  microfrontend_mode: process.env.BMAD_MICROFRONTEND_MODE === 'true' || false,
  design_system_integration: process.env.BMAD_DESIGN_SYSTEM_INTEGRATION === 'true' || false
};
