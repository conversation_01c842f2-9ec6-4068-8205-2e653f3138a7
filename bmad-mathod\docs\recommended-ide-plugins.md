# Recommended IDE Plugins for BMAD Method v4.0

This document provides recommended plugins and extensions for various IDEs to enhance your BMAD Method v4.0 development experience.

## Core Plugins (Essential for BMAD Development)

These are essential plugins recommended for TypeScript development and general productivity:

- **Cline** - AI coding assistant with custom tool integration
- **Code Spell Checker** - Spell checking for code and documentation
- **CodeMetrics** - Code complexity analysis and metrics
- **Docker** - Container development and management
- **ESLint** - JavaScript/TypeScript linting
- **Foam** - Knowledge management and note-taking (excellent for BMAD documentation)
- **Jest Runner (firstris)** - JavaScript testing framework integration
- **Markdown Preview Mermaid Support** - Enhanced markdown with diagram support
- **Monokai Charcoal high contrast** - High contrast theme for better readability
- **Playwright Test for VSCode** - End-to-end testing (if using <PERSON>wright)
- **Prettier - Code formatter** - Consistent code formatting
- **Prettier - ESLint** - Integration between <PERSON><PERSON><PERSON> and ESLint
- **SQLite** - Database management and querying

## Language-Specific Plugins

### JavaScript/TypeScript Development
- **TypeScript Importer** - Auto import for TypeScript
- **Auto Rename Tag** - Automatically rename paired HTML/XML tags
- **Bracket Pair Colorizer** - Color matching brackets
- **Path Intellisense** - Autocomplete for file paths

### Go Development
- **Go** - Official Go language support
- **Go Test Explorer** - Test discovery and execution
- **Go Outliner** - Code structure visualization

### Python Development
- **Python** - Official Python language support
- **Python Docstring Generator** - Automatic docstring generation
- **Pylance** - Advanced Python language server
- **Python Test Explorer** - Test discovery and execution

### C# Development
- **C#** - Official C# language support
- **C# Extensions** - Additional C# productivity tools
- **.NET Core Test Explorer** - Test discovery and execution
- **NuGet Package Manager** - Package management

## BMAD Method v4.0 Specific Recommendations

### Architecture and Design
- **PlantUML** - UML diagram creation and visualization
- **Draw.io Integration** - Diagram creation and editing
- **Mermaid Preview** - Live preview of Mermaid diagrams

### Microservices Development
- **Kubernetes** - Container orchestration support
- **YAML** - YAML file support for configuration
- **REST Client** - API testing and development
- **Thunder Client** - Lightweight REST API client

### AI and Machine Learning
- **Jupyter** - Notebook support for data analysis
- **Python** - Essential for AI/ML development
- **GitHub Copilot** - AI-powered code completion

### Frontend Development (Microfrontends)
- **Auto Close Tag** - Automatically close HTML/XML tags
- **HTML CSS Support** - Enhanced CSS support in HTML
- **Live Server** - Local development server
- **Tailwind CSS IntelliSense** - Tailwind CSS autocomplete

### DevOps and Platform Engineering
- **Docker** - Container development
- **Kubernetes** - Container orchestration
- **Terraform** - Infrastructure as code
- **AWS Toolkit** - AWS cloud development
- **Azure Account** - Azure cloud development
- **Google Cloud Code** - Google Cloud development

## IDE-Specific Recommendations

### Cursor IDE
- **Cursor-specific AI features** - Leverage built-in AI capabilities
- **Custom modes configuration** - Set up BMAD Method agents
- **File tree enhancements** - Better project navigation

### Windsurf IDE
- **Cascade AI integration** - Utilize Windsurf's agentic features
- **Flow configuration** - Set up BMAD workflows
- **MCP extensions** - Model Context Protocol integrations

### RooCode
- **Multi-model support** - Configure different models per BMAD role
- **Cost tracking** - Monitor usage and optimize model selection
- **Custom mode templates** - Pre-configured BMAD agent modes

## Configuration Tips

### Plugin Management
1. **Install gradually** - Add plugins as needed to avoid overwhelming the IDE
2. **Configure settings** - Customize plugin settings for your workflow
3. **Regular updates** - Keep plugins updated for latest features and security

### Performance Optimization
1. **Disable unused plugins** - Remove or disable plugins you don't actively use
2. **Monitor resource usage** - Check plugin impact on IDE performance
3. **Optimize settings** - Configure plugins for optimal performance

### Team Collaboration
1. **Share configurations** - Use workspace settings for team consistency
2. **Document plugin requirements** - List required plugins in project documentation
3. **Version control settings** - Include relevant configuration files in version control

## BMAD Method Integration

### Workflow Enhancement
- Use **Foam** for maintaining BMAD documentation and knowledge base
- Configure **Mermaid Preview** for visualizing BMAD workflows
- Set up **REST Client** for testing microservices APIs

### Quality Assurance
- Configure **ESLint** and **Prettier** for consistent code quality
- Use **CodeMetrics** to monitor complexity in microservices
- Set up **Jest Runner** or **Playwright** for comprehensive testing

### Documentation
- Use **Code Spell Checker** for documentation quality
- Configure **Markdown Preview Mermaid Support** for enhanced documentation
- Set up **PlantUML** for architecture diagrams

This plugin configuration ensures optimal productivity when working with BMAD Method v4.0 across different development environments and technology stacks.
