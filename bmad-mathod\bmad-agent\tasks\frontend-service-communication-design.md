# Frontend Service Communication Design Task

## Objective
Design comprehensive communication patterns and integration strategies for microfrontends to interact with backend services, other microfrontends, and shared infrastructure components.

## Context
You are designing the communication architecture for a distributed frontend system where multiple microfrontends need to coordinate, share data, and integrate with various backend services while maintaining loose coupling and high performance.

## Prerequisites
- Review microfrontend decomposition analysis
- Understand backend service architecture
- Identify data flow requirements between components
- Assess performance and security requirements
- Review existing API contracts and integration patterns

## Task Instructions

### 1. Communication Pattern Analysis

#### Inter-Microfrontend Communication
Design patterns for microfrontend-to-microfrontend communication:

```markdown
# Inter-Microfrontend Communication Patterns

## Event-Driven Communication
### Custom Events Pattern
**Use Cases**:
- User action notifications (login, logout, navigation)
- Data change notifications
- UI state synchronization

**Implementation**:
```typescript
// Event definitions
interface MicrofrontendEvents {
  'user.authenticated': { userId: string; token: string };
  'navigation.changed': { route: string; params: Record<string, any> };
  'data.updated': { entity: string; id: string; data: any };
  'theme.changed': { theme: 'light' | 'dark' };
}

// Event bus implementation
class EventBus {
  private listeners: Map<string, Function[]> = new Map();
  
  publish<K extends keyof MicrofrontendEvents>(
    event: K, 
    data: MicrofrontendEvents[K]
  ): void {
    const eventListeners = this.listeners.get(event) || [];
    eventListeners.forEach(listener => listener(data));
  }
  
  subscribe<K extends keyof MicrofrontendEvents>(
    event: K, 
    handler: (data: MicrofrontendEvents[K]) => void
  ): () => void {
    const eventListeners = this.listeners.get(event) || [];
    eventListeners.push(handler);
    this.listeners.set(event, eventListeners);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(event) || [];
      const index = listeners.indexOf(handler);
      if (index > -1) listeners.splice(index, 1);
    };
  }
}
```

### Shared State Management
**Use Cases**:
- Global application state (user session, preferences)
- Cross-microfrontend data sharing
- Real-time data synchronization

**Implementation**:
```typescript
// Zustand-based shared store
interface GlobalStore {
  user: User | null;
  theme: 'light' | 'dark';
  notifications: Notification[];
  setUser: (user: User | null) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  addNotification: (notification: Notification) => void;
}

const useGlobalStore = create<GlobalStore>((set) => ({
  user: null,
  theme: 'light',
  notifications: [],
  setUser: (user) => set({ user }),
  setTheme: (theme) => set({ theme }),
  addNotification: (notification) => 
    set((state) => ({ 
      notifications: [...state.notifications, notification] 
    })),
}));
```

### Direct Component Integration
**Use Cases**:
- Parent-child component relationships
- Shared component libraries
- Tightly coupled UI components

**Implementation**:
```typescript
// Component props interface
interface MicrofrontendComponentProps {
  data?: any;
  onAction?: (action: string, payload: any) => void;
  config?: ComponentConfig;
  theme?: ThemeConfig;
}

// Microfrontend wrapper component
const MicrofrontendWrapper: React.FC<{
  microfrontend: string;
  props?: MicrofrontendComponentProps;
}> = ({ microfrontend, props }) => {
  const Component = useMicrofrontendComponent(microfrontend);
  
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingSpinner />}>
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};
```

## Backend Service Integration
### API Gateway Pattern
**Architecture**:
```
Microfrontends → API Gateway → Backend Services
```

**Benefits**:
- Centralized authentication and authorization
- Request/response transformation
- Rate limiting and caching
- Service discovery and load balancing

**Implementation**:
```typescript
// API client configuration
interface APIClientConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  headers: Record<string, string>;
}

class APIClient {
  private config: APIClientConfig;
  private interceptors: {
    request: Function[];
    response: Function[];
  } = { request: [], response: [] };
  
  constructor(config: APIClientConfig) {
    this.config = config;
  }
  
  async request<T>(
    method: string,
    url: string,
    data?: any,
    options?: RequestOptions
  ): Promise<T> {
    // Apply request interceptors
    let requestConfig = { method, url, data, ...options };
    for (const interceptor of this.interceptors.request) {
      requestConfig = await interceptor(requestConfig);
    }
    
    // Make request with retry logic
    let lastError: Error;
    for (let attempt = 0; attempt <= this.config.retries; attempt++) {
      try {
        const response = await fetch(requestConfig.url, {
          method: requestConfig.method,
          headers: {
            'Content-Type': 'application/json',
            ...this.config.headers,
            ...requestConfig.headers,
          },
          body: requestConfig.data ? JSON.stringify(requestConfig.data) : undefined,
          signal: AbortSignal.timeout(this.config.timeout),
        });
        
        if (!response.ok) {
          throw new APIError(response.status, await response.text());
        }
        
        let result = await response.json();
        
        // Apply response interceptors
        for (const interceptor of this.interceptors.response) {
          result = await interceptor(result);
        }
        
        return result;
      } catch (error) {
        lastError = error as Error;
        if (attempt < this.config.retries) {
          await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
        }
      }
    }
    
    throw lastError!;
  }
  
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### Backend for Frontend (BFF) Pattern
**Use Cases**:
- Microfrontend-specific data aggregation
- Protocol translation (REST to GraphQL)
- Response optimization for frontend needs

**Implementation**:
```typescript
// BFF service interface
interface UserBFFService {
  getUserDashboard(userId: string): Promise<UserDashboard>;
  getUserProfile(userId: string): Promise<UserProfile>;
  updateUserPreferences(userId: string, preferences: UserPreferences): Promise<void>;
}

// BFF implementation
class UserBFFServiceImpl implements UserBFFService {
  constructor(
    private userService: UserService,
    private orderService: OrderService,
    private notificationService: NotificationService
  ) {}
  
  async getUserDashboard(userId: string): Promise<UserDashboard> {
    // Aggregate data from multiple services
    const [user, recentOrders, notifications] = await Promise.all([
      this.userService.getUser(userId),
      this.orderService.getRecentOrders(userId, 5),
      this.notificationService.getUnreadNotifications(userId)
    ]);
    
    return {
      user: {
        id: user.id,
        name: user.name,
        avatar: user.avatar,
      },
      recentOrders: recentOrders.map(order => ({
        id: order.id,
        status: order.status,
        total: order.total,
        date: order.createdAt,
      })),
      notifications: notifications.map(notification => ({
        id: notification.id,
        message: notification.message,
        type: notification.type,
        timestamp: notification.createdAt,
      })),
    };
  }
}
```

## Real-Time Communication
### WebSocket Integration
**Use Cases**:
- Live notifications
- Real-time data updates
- Collaborative features

**Implementation**:
```typescript
// WebSocket manager
class WebSocketManager {
  private connections: Map<string, WebSocket> = new Map();
  private eventBus: EventBus;
  
  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }
  
  connect(endpoint: string, protocols?: string[]): Promise<WebSocket> {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(endpoint, protocols);
      
      ws.onopen = () => {
        this.connections.set(endpoint, ws);
        resolve(ws);
      };
      
      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.eventBus.publish(message.type, message.payload);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };
      
      ws.onerror = (error) => {
        reject(error);
      };
      
      ws.onclose = () => {
        this.connections.delete(endpoint);
        // Implement reconnection logic
        setTimeout(() => this.connect(endpoint, protocols), 5000);
      };
    });
  }
  
  send(endpoint: string, message: any): void {
    const ws = this.connections.get(endpoint);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }
}
```

### Server-Sent Events (SSE)
**Use Cases**:
- Unidirectional real-time updates
- Live feeds and notifications
- Progress tracking

**Implementation**:
```typescript
// SSE client
class SSEClient {
  private eventSource: EventSource | null = null;
  private eventBus: EventBus;
  
  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }
  
  connect(url: string): void {
    this.eventSource = new EventSource(url);
    
    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.eventBus.publish(data.type, data.payload);
      } catch (error) {
        console.error('Failed to parse SSE message:', error);
      }
    };
    
    this.eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      // Implement reconnection logic
    };
  }
  
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}
```

## Error Handling and Resilience
### Circuit Breaker Pattern
```typescript
// Circuit breaker implementation
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private threshold: number = 5,
    private timeout: number = 60000,
    private monitoringPeriod: number = 10000
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime >= this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}
```

### Retry and Fallback Strategies
```typescript
// Retry with exponential backoff
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError!;
}

// Fallback data provider
class FallbackDataProvider {
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private cacheTTL = 5 * 60 * 1000; // 5 minutes
  
  async getWithFallback<T>(
    key: string,
    primaryProvider: () => Promise<T>,
    fallbackProvider?: () => Promise<T>
  ): Promise<T> {
    try {
      const data = await primaryProvider();
      this.cache.set(key, { data, timestamp: Date.now() });
      return data;
    } catch (error) {
      // Try fallback provider
      if (fallbackProvider) {
        try {
          return await fallbackProvider();
        } catch (fallbackError) {
          // Fall back to cache
          return this.getCachedData(key);
        }
      }
      
      // Fall back to cache
      return this.getCachedData(key);
    }
  }
  
  private getCachedData<T>(key: string): T {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached.data;
    }
    throw new Error(`No fallback data available for ${key}`);
  }
}
```

## Performance Optimization
### Request Batching
```typescript
// Request batcher
class RequestBatcher {
  private batches: Map<string, {
    requests: Array<{ resolve: Function; reject: Function; params: any }>;
    timer: NodeJS.Timeout;
  }> = new Map();
  
  private batchDelay = 50; // 50ms
  
  async batchRequest<T>(
    batchKey: string,
    params: any,
    batchExecutor: (requests: any[]) => Promise<T[]>
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      let batch = this.batches.get(batchKey);
      
      if (!batch) {
        batch = {
          requests: [],
          timer: setTimeout(() => this.executeBatch(batchKey, batchExecutor), this.batchDelay)
        };
        this.batches.set(batchKey, batch);
      }
      
      batch.requests.push({ resolve, reject, params });
    });
  }
  
  private async executeBatch<T>(
    batchKey: string,
    batchExecutor: (requests: any[]) => Promise<T[]>
  ): Promise<void> {
    const batch = this.batches.get(batchKey);
    if (!batch) return;
    
    this.batches.delete(batchKey);
    
    try {
      const params = batch.requests.map(req => req.params);
      const results = await batchExecutor(params);
      
      batch.requests.forEach((req, index) => {
        req.resolve(results[index]);
      });
    } catch (error) {
      batch.requests.forEach(req => {
        req.reject(error);
      });
    }
  }
}
```

### Caching Strategy
```typescript
// Multi-layer cache
class CacheManager {
  private memoryCache: Map<string, { data: any; expiry: number }> = new Map();
  private localStorage: Storage;
  
  constructor() {
    this.localStorage = window.localStorage;
  }
  
  async get<T>(key: string): Promise<T | null> {
    // Check memory cache first
    const memoryData = this.memoryCache.get(key);
    if (memoryData && Date.now() < memoryData.expiry) {
      return memoryData.data;
    }
    
    // Check localStorage
    try {
      const localData = this.localStorage.getItem(key);
      if (localData) {
        const parsed = JSON.parse(localData);
        if (Date.now() < parsed.expiry) {
          // Promote to memory cache
          this.memoryCache.set(key, parsed);
          return parsed.data;
        }
      }
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
    }
    
    return null;
  }
  
  set<T>(key: string, data: T, ttl: number = 300000): void { // 5 minutes default
    const expiry = Date.now() + ttl;
    const cacheEntry = { data, expiry };
    
    // Set in memory cache
    this.memoryCache.set(key, cacheEntry);
    
    // Set in localStorage
    try {
      this.localStorage.setItem(key, JSON.stringify(cacheEntry));
    } catch (error) {
      console.warn('Failed to write to localStorage:', error);
    }
  }
  
  invalidate(key: string): void {
    this.memoryCache.delete(key);
    try {
      this.localStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error);
    }
  }
}
```

## Security Considerations
### Token Management
```typescript
// Secure token manager
class TokenManager {
  private tokenKey = 'auth_token';
  private refreshTokenKey = 'refresh_token';
  
  setTokens(accessToken: string, refreshToken: string): void {
    // Store in httpOnly cookie for security
    document.cookie = `${this.tokenKey}=${accessToken}; Secure; SameSite=Strict; Path=/`;
    document.cookie = `${this.refreshTokenKey}=${refreshToken}; Secure; SameSite=Strict; Path=/`;
  }
  
  getAccessToken(): string | null {
    return this.getCookie(this.tokenKey);
  }
  
  async refreshAccessToken(): Promise<string> {
    const refreshToken = this.getCookie(this.refreshTokenKey);
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }
    
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to refresh token');
    }
    
    const { accessToken, refreshToken: newRefreshToken } = await response.json();
    this.setTokens(accessToken, newRefreshToken);
    
    return accessToken;
  }
  
  private getCookie(name: string): string | null {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  }
}
```

## Output Requirements
1. **Communication Architecture Document**: Comprehensive communication patterns and strategies
2. **API Integration Specifications**: Detailed API client implementations and patterns
3. **Event Schema Definitions**: Type-safe event definitions for inter-microfrontend communication
4. **Error Handling Strategy**: Resilience patterns and fallback mechanisms
5. **Performance Optimization Plan**: Caching, batching, and optimization strategies
6. **Security Implementation**: Token management and secure communication patterns

## Quality Checklist
- [ ] Communication patterns are well-defined and documented
- [ ] Error handling and resilience strategies are comprehensive
- [ ] Performance optimization techniques are implemented
- [ ] Security considerations are addressed
- [ ] Type safety is maintained across all communication interfaces
- [ ] Monitoring and observability are integrated
- [ ] Fallback and degradation strategies are defined

## Success Criteria
The communication design is successful when:
1. **Loose Coupling**: Microfrontends can communicate without tight dependencies
2. **Performance**: Communication is optimized for speed and efficiency
3. **Reliability**: Robust error handling and fallback mechanisms
4. **Security**: Secure communication channels and token management
5. **Scalability**: Communication patterns scale with system growth
6. **Maintainability**: Clear interfaces and well-documented patterns

## Notes
- Consider implementing communication patterns incrementally
- Monitor performance impact of communication overhead
- Regularly review and optimize communication patterns
- Ensure all team members understand the communication architecture
