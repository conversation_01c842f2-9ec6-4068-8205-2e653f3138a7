{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf", "overrides": [{"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always"}}, {"files": "*.yml", "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.json", "options": {"tabWidth": 2, "singleQuote": false}}]}