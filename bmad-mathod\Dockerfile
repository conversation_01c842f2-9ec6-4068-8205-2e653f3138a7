# BMAD Method Build Environment
FROM node:18.19.0-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    bash \
    curl

# Copy package files
COPY package*.json ./
COPY .nvmrc ./

# Install dependencies
RUN npm ci --only=production

# Development stage
FROM base AS development

# Install all dependencies including dev dependencies
RUN npm ci

# Copy source code
COPY . .

# Create build directory
RUN mkdir -p build

# Set environment variables
ENV NODE_ENV=development
ENV BMAD_BUILD_DIR=/app/build
ENV BMAD_ASSET_ROOT=/app/bmad-agent

# Expose port for development server (if needed)
EXPOSE 3000

# Default command for development
CMD ["npm", "run", "dev"]

# Production build stage
FROM base AS build

# Copy source code
COPY . .

# Set production environment
ENV NODE_ENV=production

# Build the web agent bundle
RUN npm run build

# Validate the build
RUN npm run validate:build

# Production stage
FROM node:18.19.0-alpine AS production

# Set working directory
WORKDIR /app

# Install only runtime dependencies
RUN apk add --no-cache bash

# Copy built application
COPY --from=build /app/build ./build
COPY --from=build /app/package.json ./
COPY --from=build /app/README.md ./
COPY --from=build /app/docs ./docs

# Create non-root user
RUN addgroup -g 1001 -S bmad && \
    adduser -S bmad -u 1001

# Change ownership
RUN chown -R bmad:bmad /app

# Switch to non-root user
USER bmad

# Set environment variables
ENV NODE_ENV=production
ENV BMAD_VERSION=4.0.0

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD test -f /app/build/agent-prompt.txt || exit 1

# Default command
CMD ["echo", "BMAD Method web agent bundle ready in /app/build/"]

# Utility stage for CI/CD
FROM base AS ci

# Install all dependencies
RUN npm ci

# Copy source code
COPY . .

# Set CI environment
ENV NODE_ENV=test
ENV CI=true

# Default command for CI
CMD ["npm", "test"]
