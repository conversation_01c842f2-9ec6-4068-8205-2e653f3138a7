#!/bin/bash
# BMAD Method Setup Script for Unix/Linux/macOS
# Run this script to set up the BMAD development environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Parse command line arguments
SKIP_NODE_CHECK=false
FORCE=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-node-check)
            SKIP_NODE_CHECK=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "BMAD Method Setup Script"
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --skip-node-check    Skip Node.js version checking"
            echo "  --force             Force overwrite existing files"
            echo "  --verbose           Enable verbose output"
            echo "  -h, --help          Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo -e "${CYAN}🚀 BMAD Method 4.0 Setup${NC}"
echo -e "${CYAN}================================${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Node.js installation
if [ "$SKIP_NODE_CHECK" = false ]; then
    echo -e "${YELLOW}📋 Checking Node.js installation...${NC}"
    
    if ! command_exists node; then
        echo -e "${RED}❌ Node.js is not installed or not in PATH${NC}"
        echo -e "${RED}Please install Node.js 16.0.0 or higher from https://nodejs.org/${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js version: $NODE_VERSION${NC}"
    
    # Check Node.js version
    MAJOR_VERSION=$(echo $NODE_VERSION | sed 's/v//' | cut -d. -f1)
    
    if [ "$MAJOR_VERSION" -lt 16 ]; then
        echo -e "${RED}❌ Node.js version $NODE_VERSION is too old. Please upgrade to 16.0.0 or higher.${NC}"
        exit 1
    fi
fi

# Check npm installation
echo -e "${YELLOW}📋 Checking npm installation...${NC}"
if ! command_exists npm; then
    echo -e "${RED}❌ npm is not installed or not in PATH${NC}"
    exit 1
fi

NPM_VERSION=$(npm --version)
echo -e "${GREEN}✅ npm version: $NPM_VERSION${NC}"

# Create .env file if it doesn't exist
echo -e "${YELLOW}📋 Setting up environment configuration...${NC}"
if [ ! -f ".env" ] || [ "$FORCE" = true ]; then
    if [ -f ".env.example" ]; then
        cp ".env.example" ".env"
        echo -e "${GREEN}✅ Created .env file from template${NC}"
    else
        echo -e "${YELLOW}⚠️  .env.example not found, skipping .env creation${NC}"
    fi
else
    echo -e "${GREEN}✅ .env file already exists${NC}"
fi

# Install dependencies
echo -e "${YELLOW}📋 Installing dependencies...${NC}"
if npm install; then
    echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
else
    echo -e "${RED}❌ Failed to install dependencies${NC}"
    exit 1
fi

# Run initial build
echo -e "${YELLOW}📋 Running initial build...${NC}"
if npm run build; then
    echo -e "${GREEN}✅ Initial build completed successfully${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# Validate setup
echo -e "${YELLOW}📋 Validating setup...${NC}"
if npm test; then
    echo -e "${GREEN}✅ Setup validation passed${NC}"
else
    echo -e "${RED}❌ Setup validation failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 BMAD Method setup completed successfully!${NC}"
echo ""
echo -e "${CYAN}Next steps:${NC}"
echo -e "${WHITE}1. Review the generated files in ./build/${NC}"
echo -e "${WHITE}2. Follow the Web Quickstart guide in README.md${NC}"
echo -e "${WHITE}3. Copy bmad-agent/ folder to your project root for IDE usage${NC}"
echo ""
echo -e "${CYAN}Available commands:${NC}"
echo -e "${WHITE}  npm run build        - Build web agent bundles${NC}"
echo -e "${WHITE}  npm run dev          - Clean and rebuild${NC}"
echo -e "${WHITE}  npm run deploy:web   - Prepare for web deployment${NC}"
echo -e "${WHITE}  npm test             - Validate configuration${NC}"
