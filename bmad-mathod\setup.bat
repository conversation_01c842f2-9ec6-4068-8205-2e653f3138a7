@echo off
REM BMAD Method Setup Script for Windows Command Prompt
REM Run this script to set up the BMAD development environment

setlocal enabledelayedexpansion

echo.
echo 🚀 BMAD Method 4.0 Setup
echo ================================

REM Check Node.js installation
echo 📋 Checking Node.js installation...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js 16.0.0 or higher from https://nodejs.org/
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js version: %NODE_VERSION%

REM Check npm installation
echo 📋 Checking npm installation...
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm is not installed or not in PATH
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm version: %NPM_VERSION%

REM Create .env file if it doesn't exist
echo 📋 Setting up environment configuration...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ Created .env file from template
    ) else (
        echo ⚠️  .env.example not found, skipping .env creation
    )
) else (
    echo ✅ .env file already exists
)

REM Install dependencies
echo 📋 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    exit /b 1
)
echo ✅ Dependencies installed successfully

REM Run initial build
echo 📋 Running initial build...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    exit /b 1
)
echo ✅ Initial build completed successfully

REM Validate setup
echo 📋 Validating setup...
call npm test
if %errorlevel% neq 0 (
    echo ❌ Setup validation failed
    exit /b 1
)
echo ✅ Setup validation passed

echo.
echo 🎉 BMAD Method setup completed successfully!
echo.
echo Next steps:
echo 1. Review the generated files in ./build/
echo 2. Follow the Web Quickstart guide in README.md
echo 3. Copy bmad-agent/ folder to your project root for IDE usage
echo.
echo Available commands:
echo   npm run build        - Build web agent bundles
echo   npm run dev          - Clean and rebuild
echo   npm run deploy:web   - Prepare for web deployment
echo   npm test             - Validate configuration

pause
