# API Gateway Configuration Template

## Document Information
- **Document Type**: API Gateway Configuration
- **Version**: 1.0
- **Status**: [Draft/Review/Approved]
- **Author**: [Service Mesh Architect Name]
- **Date**: [Creation Date]
- **Last Updated**: [Last Update Date]

## Executive Summary

### Gateway Overview
- **Project Name**: [Project Name]
- **Gateway Type**: [Kong/NGINX/AWS API Gateway/Istio Gateway]
- **Deployment Model**: [Centralized/Distributed/Hybrid]
- **Microfrontend Count**: [Number of microfrontends]
- **Backend Services**: [Number of backend services]

### Key Objectives
- **Unified Entry Point**: Single point of access for all frontend services
- **Security Enforcement**: Authentication, authorization, and rate limiting
- **Traffic Management**: Load balancing, routing, and failover
- **Observability**: Logging, monitoring, and tracing
- **Protocol Translation**: HTTP/HTTPS, WebSocket, gRPC support

## Gateway Architecture

### High-Level Architecture
```
Internet/CDN
    ↓
Load Balancer (L4/L7)
    ↓
API Gateway Cluster
├── Authentication Service
├── Authorization Service
├── Rate Limiting Service
├── Caching Layer
└── Monitoring/Logging
    ↓
Service Mesh (Istio/Linkerd)
    ↓
Backend Services
├── User Service
├── Product Service
├── Order Service
└── Notification Service
```

### Gateway Deployment
```yaml
# Kubernetes deployment for API Gateway
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  labels:
    app: api-gateway
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        version: v1
    spec:
      containers:
      - name: gateway
        image: kong:3.4
        ports:
        - containerPort: 8000
          name: proxy
        - containerPort: 8001
          name: admin
        env:
        - name: KONG_DATABASE
          value: "postgres"
        - name: KONG_PG_HOST
          value: "postgres-service"
        - name: KONG_PROXY_ACCESS_LOG
          value: "/dev/stdout"
        - name: KONG_ADMIN_ACCESS_LOG
          value: "/dev/stdout"
        - name: KONG_PROXY_ERROR_LOG
          value: "/dev/stderr"
        - name: KONG_ADMIN_ERROR_LOG
          value: "/dev/stderr"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /status
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /status
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Routing Configuration

### Frontend Service Routing
```yaml
# Kong service and route configuration
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: shell-application
spec:
  host: shell-app-service
  port: 80
  protocol: http
  path: /
---
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: shell-app-route
spec:
  service: shell-application
  protocols:
  - http
  - https
  hosts:
  - app.company.com
  paths:
  - /
  strip_path: false
---
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: user-microfrontend
spec:
  host: user-mf-service
  port: 80
  protocol: http
  path: /
---
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: user-mf-route
spec:
  service: user-microfrontend
  protocols:
  - http
  - https
  hosts:
  - app.company.com
  paths:
  - /user
  - /profile
  strip_path: false
```

### Backend API Routing
```yaml
# Backend service routing
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: user-api
spec:
  host: user-api-service
  port: 8080
  protocol: http
  path: /api/v1
---
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: user-api-route
spec:
  service: user-api
  protocols:
  - http
  - https
  hosts:
  - api.company.com
  paths:
  - /api/users
  strip_path: true
  plugins:
  - name: rate-limiting
    config:
      minute: 100
      hour: 1000
  - name: cors
    config:
      origins:
      - "https://app.company.com"
      methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
      headers:
      - Accept
      - Authorization
      - Content-Type
      - X-Requested-With
      credentials: true
```

## Security Configuration

### Authentication Setup
```yaml
# OAuth 2.0 / OIDC Plugin Configuration
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: oidc-auth
plugin: openid-connect
config:
  issuer: "https://auth.company.com/realms/company"
  client_id: "api-gateway"
  client_secret: "gateway-secret"
  redirect_uri: "https://app.company.com/auth/callback"
  scope: "openid profile email"
  response_type: "code"
  ssl_verify: true
  session_secret: "session-secret-key"
  recovery_page_path: "/auth/error"
---
# Apply to routes requiring authentication
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: protected-route
  annotations:
    konghq.com/plugins: oidc-auth
spec:
  service: protected-service
  protocols:
  - https
  hosts:
  - api.company.com
  paths:
  - /api/protected
```

### JWT Validation
```yaml
# JWT Plugin Configuration
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: jwt-auth
plugin: jwt
config:
  uri_param_names:
  - jwt
  header_names:
  - Authorization
  claims_to_verify:
  - exp
  - iat
  - iss
  - aud
  key_claim_name: iss
  secret_is_base64: false
  run_on_preflight: true
---
# JWT Consumer
apiVersion: configuration.konghq.com/v1
kind: KongConsumer
metadata:
  name: frontend-app
username: frontend-app
---
apiVersion: configuration.konghq.com/v1
kind: KongCredential-jwt
metadata:
  name: frontend-jwt
consumerRef: frontend-app
type: jwt
config:
  key: "frontend-app"
  secret: "jwt-secret-key"
  algorithm: "HS256"
```

### Rate Limiting
```yaml
# Rate Limiting Configuration
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: api-rate-limit
plugin: rate-limiting
config:
  minute: 100
  hour: 1000
  day: 10000
  policy: "redis"
  redis_host: "redis-service"
  redis_port: 6379
  redis_database: 0
  hide_client_headers: false
  fault_tolerant: true
---
# Advanced Rate Limiting by Consumer
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: consumer-rate-limit
plugin: rate-limiting-advanced
config:
  limit:
  - 100
  window_size:
  - 60
  identifier: consumer
  sync_rate: 10
  strategy: redis
  redis:
    host: redis-service
    port: 6379
    database: 0
```

## Load Balancing and Health Checks

### Upstream Configuration
```yaml
# Upstream service configuration
apiVersion: configuration.konghq.com/v1
kind: KongUpstream
metadata:
  name: user-service-upstream
spec:
  name: user-service
  algorithm: round-robin
  hash_on: none
  hash_fallback: none
  healthchecks:
    active:
      type: http
      http_path: /health
      healthy:
        interval: 10
        successes: 3
      unhealthy:
        interval: 10
        http_failures: 3
        timeouts: 3
    passive:
      type: http
      healthy:
        successes: 3
      unhealthy:
        http_failures: 3
        timeouts: 3
---
# Target endpoints
apiVersion: configuration.konghq.com/v1
kind: KongTarget
metadata:
  name: user-service-target-1
spec:
  upstream: user-service
  target: user-service-1:8080
  weight: 100
---
apiVersion: configuration.konghq.com/v1
kind: KongTarget
metadata:
  name: user-service-target-2
spec:
  upstream: user-service
  target: user-service-2:8080
  weight: 100
```

### Circuit Breaker
```yaml
# Circuit Breaker Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: circuit-breaker
plugin: proxy-cache
config:
  response_code:
  - 200
  - 301
  - 404
  request_method:
  - GET
  - HEAD
  content_type:
  - text/plain
  - application/json
  cache_ttl: 300
  cache_control: false
  storage_ttl: 3600
---
# Timeout configuration
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: timeout-service
spec:
  host: backend-service
  port: 8080
  protocol: http
  connect_timeout: 5000
  write_timeout: 10000
  read_timeout: 10000
  retries: 3
```

## Caching Strategy

### Response Caching
```yaml
# Proxy Cache Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: response-cache
plugin: proxy-cache
config:
  response_code:
  - 200
  - 301
  - 404
  request_method:
  - GET
  - HEAD
  content_type:
  - text/plain
  - application/json
  - text/html
  cache_ttl: 300
  cache_control: true
  storage_ttl: 3600
  strategy: memory
  memory:
    dictionary_name: kong_db_cache
---
# Redis-based caching
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: redis-cache
plugin: proxy-cache
config:
  strategy: redis
  redis:
    host: redis-service
    port: 6379
    database: 1
    timeout: 2000
  cache_ttl: 600
  storage_ttl: 7200
```

### CDN Integration
```yaml
# CDN Cache Control Headers
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cdn-headers
plugin: response-transformer
config:
  add:
    headers:
    - "Cache-Control: public, max-age=3600"
    - "CDN-Cache-Control: max-age=86400"
    - "Vary: Accept-Encoding"
  remove:
    headers:
    - "Server"
    - "X-Powered-By"
```

## Monitoring and Observability

### Logging Configuration
```yaml
# HTTP Log Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: http-log
plugin: http-log
config:
  http_endpoint: "https://logs.company.com/api/logs"
  method: POST
  timeout: 10000
  keepalive: 60000
  content_type: application/json
  flush_timeout: 2
  retry_count: 10
  queue_size: 1
---
# File Log Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: file-log
plugin: file-log
config:
  path: /var/log/kong/access.log
  reopen: true
```

### Metrics Collection
```yaml
# Prometheus Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: prometheus
plugin: prometheus
config:
  per_consumer: true
  status_code_metrics: true
  latency_metrics: true
  bandwidth_metrics: true
  upstream_health_metrics: true
---
# StatsD Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: statsd
plugin: statsd
config:
  host: statsd-service
  port: 8125
  metrics:
  - request_count
  - request_size
  - response_size
  - latency
  - status_count
  - unique_users
  - request_per_user
  - upstream_latency
  - kong_latency
```

### Distributed Tracing
```yaml
# Zipkin Tracing Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: zipkin
plugin: zipkin
config:
  http_endpoint: "http://zipkin-service:9411/api/v2/spans"
  sample_ratio: 0.1
  include_credential: true
  traceid_byte_count: 16
  header_type: preserve
  default_header_type: b3
  tags:
    service: api-gateway
    environment: production
```

## Error Handling and Resilience

### Error Response Templates
```yaml
# Custom Error Pages
apiVersion: v1
kind: ConfigMap
metadata:
  name: error-pages
data:
  404.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>Page Not Found</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error-code { font-size: 72px; color: #e74c3c; }
            .error-message { font-size: 24px; color: #34495e; }
        </style>
    </head>
    <body>
        <div class="error-code">404</div>
        <div class="error-message">The page you're looking for doesn't exist.</div>
        <a href="/">Return to Home</a>
    </body>
    </html>
  
  500.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>Server Error</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error-code { font-size: 72px; color: #e74c3c; }
            .error-message { font-size: 24px; color: #34495e; }
        </style>
    </head>
    <body>
        <div class="error-code">500</div>
        <div class="error-message">Something went wrong on our end.</div>
        <a href="/">Return to Home</a>
    </body>
    </html>
```

### Retry and Timeout Configuration
```yaml
# Request Termination Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: request-termination
plugin: request-termination
config:
  status_code: 503
  content_type: application/json
  body: '{"error": "Service temporarily unavailable"}'
  trigger: "X-Terminate"
---
# Request Size Limiting
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: request-size-limit
plugin: request-size-limiting
config:
  allowed_payload_size: 10
  size_unit: megabytes
  require_content_length: false
```

## Security Headers and CORS

### Security Headers
```yaml
# Security Headers Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: security-headers
plugin: response-transformer
config:
  add:
    headers:
    - "X-Content-Type-Options: nosniff"
    - "X-Frame-Options: DENY"
    - "X-XSS-Protection: 1; mode=block"
    - "Strict-Transport-Security: max-age=31536000; includeSubDomains"
    - "Referrer-Policy: strict-origin-when-cross-origin"
    - "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
  remove:
    headers:
    - "Server"
    - "X-Powered-By"
```

### CORS Configuration
```yaml
# CORS Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cors-policy
plugin: cors
config:
  origins:
  - "https://app.company.com"
  - "https://admin.company.com"
  methods:
  - GET
  - POST
  - PUT
  - DELETE
  - OPTIONS
  - HEAD
  headers:
  - Accept
  - Accept-Version
  - Authorization
  - Content-Length
  - Content-MD5
  - Content-Type
  - Date
  - X-Auth-Token
  - X-Requested-With
  exposed_headers:
  - X-Auth-Token
  - X-RateLimit-Limit
  - X-RateLimit-Remaining
  - X-RateLimit-Reset
  credentials: true
  max_age: 3600
  preflight_continue: false
```

## Environment Configuration

### Development Environment
```yaml
# Development Gateway Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-dev-config
data:
  kong.conf: |
    database = postgres
    pg_host = postgres-dev
    pg_port = 5432
    pg_database = kong_dev
    pg_user = kong
    pg_password = kong_password
    
    proxy_listen = 0.0.0.0:8000
    admin_listen = 0.0.0.0:8001
    
    log_level = debug
    proxy_access_log = /dev/stdout
    proxy_error_log = /dev/stderr
    admin_access_log = /dev/stdout
    admin_error_log = /dev/stderr
    
    plugins = bundled,oidc,prometheus
```

### Production Environment
```yaml
# Production Gateway Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-prod-config
data:
  kong.conf: |
    database = postgres
    pg_host = postgres-prod
    pg_port = 5432
    pg_database = kong_prod
    pg_user = kong
    pg_password_file = /etc/secrets/pg_password
    
    proxy_listen = 0.0.0.0:8000 ssl
    admin_listen = 127.0.0.1:8001
    
    log_level = notice
    proxy_access_log = /var/log/kong/access.log
    proxy_error_log = /var/log/kong/error.log
    
    ssl_cert = /etc/ssl/certs/gateway.crt
    ssl_cert_key = /etc/ssl/private/gateway.key
    
    plugins = bundled,oidc,prometheus
    
    nginx_worker_processes = auto
    nginx_daemon = off
```

## Disaster Recovery and Backup

### Configuration Backup
```bash
#!/bin/bash
# Gateway configuration backup script

BACKUP_DIR="/backups/gateway/$(date +%Y%m%d_%H%M%S)"
KONG_ADMIN_URL="http://kong-admin:8001"

mkdir -p "$BACKUP_DIR"

# Export Kong configuration
curl -s "$KONG_ADMIN_URL/config" > "$BACKUP_DIR/kong-config.json"

# Export services
curl -s "$KONG_ADMIN_URL/services" > "$BACKUP_DIR/services.json"

# Export routes
curl -s "$KONG_ADMIN_URL/routes" > "$BACKUP_DIR/routes.json"

# Export plugins
curl -s "$KONG_ADMIN_URL/plugins" > "$BACKUP_DIR/plugins.json"

# Export consumers
curl -s "$KONG_ADMIN_URL/consumers" > "$BACKUP_DIR/consumers.json"

# Create archive
tar -czf "$BACKUP_DIR.tar.gz" -C "$BACKUP_DIR" .
rm -rf "$BACKUP_DIR"

echo "Backup completed: $BACKUP_DIR.tar.gz"
```

### Disaster Recovery Plan
1. **Assess Impact**: Determine scope of gateway failure
2. **Activate Backup Gateway**: Switch traffic to backup instance
3. **Restore Configuration**: Apply latest configuration backup
4. **Validate Services**: Verify all routes and plugins are working
5. **Monitor Performance**: Ensure normal operation is restored
6. **Post-Incident Review**: Analyze failure and improve procedures

---

*This template provides a comprehensive framework for API Gateway configuration in microfrontend architectures. Customize based on your specific gateway technology and requirements.*
