# Microfrontend Decomposition Analysis Task

## Objective
Analyze the current application or requirements to identify optimal microfrontend boundaries based on business domains, user journeys, team structure, and technical constraints.

## Context
You are performing a strategic analysis to decompose a monolithic frontend or design a new distributed frontend system using microfrontend architecture. This analysis will inform the overall system design and team organization.

## Prerequisites
- Review business requirements and user stories
- Understand current application architecture (if migrating)
- Identify team structure and organizational boundaries
- Analyze user journey maps and workflows
- Assess technical constraints and dependencies

## Task Instructions

### 1. Business Domain Analysis

#### Domain Identification
Create a comprehensive domain map:

```markdown
# Business Domain Map

## Core Domains
### Domain 1: [Domain Name]
- **Business Capability**: [Primary business function]
- **User Value**: [Value delivered to users]
- **Data Entities**: [Key data objects owned]
- **Business Rules**: [Domain-specific business logic]
- **Stakeholders**: [Business stakeholders involved]

### Domain 2: [Domain Name]
- **Business Capability**: [Primary business function]
- **User Value**: [Value delivered to users]
- **Data Entities**: [Key data objects owned]
- **Business Rules**: [Domain-specific business logic]
- **Stakeholders**: [Business stakeholders involved]

## Supporting Domains
### Shared Services
- Authentication & Authorization
- Notification System
- Audit & Logging
- Configuration Management
- File Management

### Generic Subdomains
- User Profile Management
- Settings & Preferences
- Help & Documentation
- Reporting & Analytics
```

#### Domain Boundary Validation
Apply Domain-Driven Design principles:

1. **Bounded Context Identification**
   - Define clear boundaries where domain models are consistent
   - Identify ubiquitous language within each context
   - Map context relationships and integration points

2. **Business Capability Alignment**
   - Ensure each domain represents a complete business capability
   - Validate that domains can operate independently
   - Confirm domains align with organizational structure

3. **Data Ownership Assessment**
   - Identify which domain owns which data entities
   - Define data sharing patterns and dependencies
   - Establish data consistency requirements

### 2. User Journey Mapping

#### Journey Analysis
Map user workflows across domains:

```markdown
# User Journey Analysis

## Primary User Journeys
### Journey 1: [Journey Name]
**User Goal**: [What the user wants to achieve]
**Steps**:
1. [Step 1] → Domain: [Domain Name]
2. [Step 2] → Domain: [Domain Name]
3. [Step 3] → Domain: [Domain Name]

**Cross-Domain Interactions**:
- [Domain A] → [Domain B]: [Interaction type and data]
- [Domain B] → [Domain C]: [Interaction type and data]

**Critical Path**: [Steps that must complete successfully]
**Fallback Scenarios**: [What happens if steps fail]

### Journey 2: [Journey Name]
[Repeat structure for each major journey]

## Secondary User Journeys
[Document less critical but important user flows]

## Administrative Journeys
[Document admin and operational user flows]
```

#### Journey-Domain Alignment
- Ensure journeys can be completed within domain boundaries
- Minimize cross-domain dependencies for critical paths
- Identify shared UI components needed across journeys

### 3. Team Structure Analysis

#### Team Topology Assessment
Analyze current and desired team structure:

```markdown
# Team Structure Analysis

## Current Team Structure
### Team 1: [Team Name]
- **Size**: [Number of members]
- **Skills**: [Technical skills and expertise]
- **Responsibilities**: [Current responsibilities]
- **Domain Knowledge**: [Business domain expertise]

### Team 2: [Team Name]
[Repeat for each team]

## Proposed Microfrontend Teams
### Team Alpha: [Domain Name] Team
- **Microfrontend**: [MF Name]
- **Domain Responsibility**: [Business domain]
- **Team Size**: [Recommended size: 2-8 people]
- **Required Skills**: [Technical skills needed]
- **Autonomy Level**: [High/Medium/Low]

### Team Beta: [Domain Name] Team
[Repeat for each proposed team]

## Shared Platform Team
- **Responsibility**: Design system, shared infrastructure, DevOps
- **Size**: [Number of members]
- **Skills**: [Platform engineering, DevOps, design systems]
```

#### Conway's Law Optimization
- Align microfrontend boundaries with team boundaries
- Ensure teams have full ownership of their microfrontend
- Minimize coordination overhead between teams

### 4. Technical Decomposition Strategy

#### Frontend Component Analysis
Analyze existing or planned UI components:

```markdown
# Component Decomposition Analysis

## Shared Components (Design System)
### Navigation Components
- Global Header
- Main Navigation
- Breadcrumbs
- Footer

### Form Components
- Input Fields
- Buttons
- Validation
- Form Layouts

### Data Display
- Tables
- Cards
- Lists
- Charts

## Domain-Specific Components
### [Domain Name] Components
- [Component 1]: [Description and responsibility]
- [Component 2]: [Description and responsibility]
- [Component 3]: [Description and responsibility]

### [Domain Name] Components
[Repeat for each domain]

## Cross-Cutting Concerns
- Error Handling
- Loading States
- Authentication UI
- Notification System
- Help & Documentation
```

#### Integration Pattern Analysis
Define how microfrontends will integrate:

```markdown
# Integration Pattern Analysis

## Composition Patterns
### Runtime Composition (Module Federation)
**Use Cases**:
- Dynamic loading of microfrontends
- Independent deployment cycles
- Technology diversity

**Implementation**:
- Webpack Module Federation
- Host-Remote pattern
- Shared dependency management

### Build-Time Composition
**Use Cases**:
- Static content and documentation
- Shared component libraries
- Performance-critical paths

**Implementation**:
- NPM package distribution
- Monorepo with shared packages
- Build-time bundling

## Communication Patterns
### Event-Driven Communication
- Custom events for loose coupling
- Event bus for centralized communication
- Type-safe event definitions

### Shared State Management
- Global state for cross-cutting concerns
- Local state for domain-specific data
- State synchronization strategies

### Direct Integration
- Component props for parent-child communication
- Callback functions for user interactions
- Context providers for shared data
```

### 5. Dependency Analysis

#### Technical Dependencies
Map technical dependencies between microfrontends:

```markdown
# Dependency Analysis

## Shared Dependencies
### Core Libraries
- React/Vue/Angular: [Version strategy]
- State Management: [Shared vs. independent]
- Routing: [Global vs. local routing]
- HTTP Client: [Shared configuration]

### Design System
- Component Library: [Centralized distribution]
- Design Tokens: [Token management strategy]
- Styling Framework: [CSS-in-JS vs. utility classes]

### Utility Libraries
- Date/Time: [Moment.js, date-fns, etc.]
- Validation: [Yup, Joi, Zod, etc.]
- Formatting: [Number, currency, etc.]

## Domain Dependencies
### [Domain A] Dependencies
- External APIs: [List of external services]
- Internal Services: [Backend service dependencies]
- Data Sources: [Database, cache, etc.]

### Cross-Domain Dependencies
- [Domain A] → [Domain B]: [Dependency type and reason]
- [Domain B] → [Domain C]: [Dependency type and reason]

## Dependency Management Strategy
- Shared dependency versioning
- Breaking change management
- Dependency update coordination
```

### 6. Migration Strategy (if applicable)

#### Current State Assessment
If migrating from a monolith:

```markdown
# Migration Analysis

## Current Monolith Assessment
### Architecture Overview
- Technology Stack: [Current technologies]
- Component Count: [Number of components]
- Bundle Size: [Current bundle sizes]
- Performance Metrics: [Current performance]

### Code Organization
- Feature Structure: [How features are organized]
- Shared Code: [Shared utilities and components]
- State Management: [Current state management]
- Routing: [Current routing structure]

## Migration Strategy
### Strangler Fig Pattern
**Phase 1**: [First microfrontend to extract]
- Target Domain: [Domain name]
- Extraction Complexity: [High/Medium/Low]
- Business Value: [Value of extracting this domain]
- Timeline: [Estimated timeline]

**Phase 2**: [Second microfrontend to extract]
[Repeat for each phase]

### Risk Assessment
- Technical Risks: [Potential technical challenges]
- Business Risks: [Impact on users and business]
- Mitigation Strategies: [How to address risks]
```

### 7. Decomposition Recommendations

#### Proposed Microfrontend Architecture
Create the final decomposition recommendation:

```markdown
# Recommended Microfrontend Architecture

## Shell Application
**Responsibility**: 
- Global navigation and layout
- Authentication orchestration
- Microfrontend loading and orchestration
- Error boundary management
- Performance monitoring

**Technology**: [Next.js, React, etc.]
**Team**: [Platform/Shell team]

## Microfrontend 1: [Name]
**Domain**: [Business domain]
**Responsibility**: [Key responsibilities]
**User Journeys**: [Primary user journeys served]
**Technology**: [Recommended tech stack]
**Team**: [Responsible team]
**Integration**: [How it integrates with shell and other MFs]

## Microfrontend 2: [Name]
[Repeat structure for each microfrontend]

## Shared Services
### Design System
- Component library distribution
- Design token management
- Theme and branding consistency

### Platform Services
- Authentication service
- Logging and monitoring
- Configuration management
- Error tracking
```

#### Implementation Roadmap
```markdown
# Implementation Roadmap

## Phase 1: Foundation (Weeks 1-4)
- Set up shell application
- Implement design system
- Establish CI/CD pipeline
- Create development environment

## Phase 2: Core Microfrontends (Weeks 5-12)
- Implement [Microfrontend 1]
- Implement [Microfrontend 2]
- Establish integration patterns
- Set up monitoring and logging

## Phase 3: Advanced Features (Weeks 13-20)
- Implement remaining microfrontends
- Optimize performance
- Enhance monitoring and observability
- User acceptance testing

## Phase 4: Production Deployment (Weeks 21-24)
- Production deployment
- Performance optimization
- Security hardening
- Documentation and training
```

## Output Requirements

1. **Domain Boundary Document**: Clear definition of business domains and their boundaries
2. **User Journey Maps**: Detailed user journey analysis with domain interactions
3. **Team Topology Recommendation**: Proposed team structure aligned with microfrontends
4. **Technical Architecture**: High-level technical architecture with integration patterns
5. **Migration Plan**: Step-by-step migration strategy (if applicable)
6. **Implementation Roadmap**: Detailed timeline with milestones and deliverables

## Quality Checklist

- [ ] Business domains are clearly defined and bounded
- [ ] User journeys are mapped to domain boundaries
- [ ] Team structure aligns with microfrontend boundaries
- [ ] Technical dependencies are identified and managed
- [ ] Integration patterns are well-defined
- [ ] Migration strategy is realistic and low-risk
- [ ] Implementation roadmap is detailed and achievable
- [ ] Performance and scalability considerations are addressed
- [ ] Security and compliance requirements are considered

## Success Criteria

The decomposition analysis is successful when:
1. **Clear Boundaries**: Each microfrontend has well-defined responsibilities
2. **Team Alignment**: Microfrontend boundaries align with team capabilities
3. **User Experience**: User journeys are optimally supported
4. **Technical Feasibility**: Implementation is technically achievable
5. **Business Value**: Decomposition delivers clear business benefits
6. **Risk Management**: Risks are identified and mitigation strategies defined

## Notes

- Consider starting with a pilot microfrontend to validate the approach
- Ensure stakeholder buy-in for the proposed decomposition
- Plan for iterative refinement based on implementation learnings
- Document decision rationale for future reference
