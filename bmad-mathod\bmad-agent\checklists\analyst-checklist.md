# Analyst Requirements Checklist

This checklist serves as a comprehensive framework to ensure Project Briefs and Service Briefs are complete, well-structured, and appropriately scoped for microservices development. The Analyst should systematically work through each item during the brief creation process.

## 0. BRIEF TYPE SELECTION AND VALIDATION

### 0.1 Brief Type Selection Criteria
- [ ] Correct brief type selected based on project scope and requirements
- [ ] Project briefs used for system-wide, multi-service initiatives
- [ ] Service briefs used for individual microservice development
- [ ] Clear justification for brief type selection documented
- [ ] Alignment with overall system architecture and business strategy

### 0.2 Stakeholder and Context Analysis
- [ ] All relevant stakeholders identified and engaged
- [ ] Business context and strategic objectives clearly understood
- [ ] Technical constraints and architectural principles documented
- [ ] Integration requirements with existing systems assessed
- [ ] Resource availability and timeline expectations gathered

## 1. PROJECT BRIEF QUALITY (System-Level Initiatives)

### 1.1 Project Scope and Vision
- [ ] Clear problem statement and strategic business context
- [ ] Well-defined project scope and system-wide impact assessment
- [ ] Comprehensive stakeholder identification and success criteria
- [ ] Business value proposition and competitive advantage articulated
- [ ] High-level solution approach and architecture philosophy defined

### 1.2 Microservices Architecture Context
- [ ] Service ecosystem impact assessment completed
- [ ] Cross-cutting concerns and platform requirements identified
- [ ] Integration patterns and communication strategies defined
- [ ] Service boundary implications and potential changes documented
- [ ] Event-driven architecture implications considered

### 1.3 AI Integration Strategy (if applicable)
- [ ] Agentic AI capabilities and placement strategy defined
- [ ] Human-AI collaboration patterns specified
- [ ] AI governance and ethics considerations addressed
- [ ] Multi-agent orchestration requirements documented
- [ ] AI infrastructure and scaling needs identified

### 1.4 Platform Engineering Requirements
- [ ] Developer experience improvements identified
- [ ] Infrastructure and tooling needs documented
- [ ] Operational excellence and SRE practices considered
- [ ] Internal Developer Platform requirements specified
- [ ] Platform-as-a-product approach defined

### 1.5 Implementation Framework
- [ ] Team topology and organizational alignment planned
- [ ] Technology stack and architectural decisions documented
- [ ] Risk assessment and mitigation strategies included
- [ ] Timeline and milestone framework established
- [ ] Resource allocation and coordination requirements defined

## 2. SERVICE BRIEF QUALITY (Individual Services)

### 2.1 Service Definition and Purpose
- [ ] Clear service name and identifier established
- [ ] Business purpose and value proposition articulated
- [ ] Service boundaries and responsibilities well-defined
- [ ] Position in service ecosystem documented
- [ ] Relationship to Master Project Brief established

### 2.2 Functional Requirements
- [ ] Core business capabilities and features specified
- [ ] API specifications and contracts outlined
- [ ] Data processing and business logic requirements defined
- [ ] Service-specific functionality clearly scoped
- [ ] Integration with business workflows documented

### 2.3 Integration and Communication
- [ ] Dependencies on other services identified
- [ ] Communication patterns and protocols specified
- [ ] Event publishing and consumption requirements defined
- [ ] External system integrations documented
- [ ] Service discovery and registration needs addressed

### 2.4 Data Management
- [ ] Data ownership and boundaries clearly defined
- [ ] Storage requirements and patterns specified
- [ ] Data consistency and synchronization requirements documented
- [ ] Privacy and compliance considerations addressed
- [ ] Data lifecycle management requirements included

### 2.5 Non-Functional Requirements
- [ ] Performance and scalability targets established
- [ ] Security and authentication requirements specified
- [ ] Availability and reliability expectations defined
- [ ] Monitoring and observability needs documented
- [ ] Operational and maintenance requirements included

### 2.6 AI Integration (if applicable)
- [ ] AI agent capabilities and placement defined
- [ ] Machine learning model integration requirements specified
- [ ] Human-AI collaboration patterns documented
- [ ] AI governance and ethics compliance addressed
- [ ] AI infrastructure and scaling needs identified

## 3. TECHNICAL ARCHITECTURE CONTEXT

### 3.1 Technology Stack Considerations
- [ ] Technology stack and framework selection guidance provided
- [ ] Deployment and containerization strategy considerations included
- [ ] Configuration management and secrets handling addressed
- [ ] Service mesh integration patterns considered
- [ ] Cloud platform and infrastructure requirements documented

### 3.2 Operational Requirements
- [ ] Monitoring and alerting specifications included
- [ ] Logging and tracing requirements documented
- [ ] Health check and readiness probe requirements specified
- [ ] Backup and disaster recovery procedures considered
- [ ] Incident response and escalation procedures outlined

## 4. QUALITY ASSURANCE AND VALIDATION

### 4.1 Brief Completeness
- [ ] All required sections completed according to template
- [ ] Sufficient detail provided for Product Manager PRD development
- [ ] Clear handoff instructions and guidance included
- [ ] Dependencies and integration points clearly documented
- [ ] Success criteria and acceptance criteria defined

### 4.2 Stakeholder Alignment
- [ ] All key stakeholders understand and approve brief content
- [ ] Clear decision-making processes and communication channels established
- [ ] Agreed-upon success criteria and measurement framework documented
- [ ] Governance and oversight mechanisms established
- [ ] Change management and approval processes defined

### 4.3 Technical Foundation
- [ ] Solid architectural foundation for detailed design provided
- [ ] Clear integration patterns and communication strategies documented
- [ ] Platform engineering requirements well-defined
- [ ] AI integration strategy aligned with business objectives
- [ ] Implementation feasibility and resource requirements validated

## 5. HANDOFF PREPARATION

### 5.1 Product Manager Handoff
- [ ] Clear guidance for Product Manager PRD development included
- [ ] Specific requirements and constraints documented
- [ ] Template selection guidance provided
- [ ] Success criteria and validation framework established
- [ ] Stakeholder communication plan included

### 5.2 Architecture Team Coordination
- [ ] Platform Architect engagement requirements identified
- [ ] Service Mesh Architect coordination needs documented
- [ ] AI Orchestration Specialist requirements specified
- [ ] Cross-functional team structure and communication established
- [ ] Technical review and approval processes defined

## 6. DOCUMENTATION AND COMMUNICATION

### 6.1 Documentation Standards
- [ ] Brief follows appropriate template structure and format
- [ ] Clear, concise, and professional language used throughout
- [ ] Technical terms and concepts properly defined and explained
- [ ] Visual aids and diagrams included where appropriate
- [ ] Version control and change tracking implemented

### 6.2 Communication and Collaboration
- [ ] Stakeholder communication plan established
- [ ] Regular review and feedback mechanisms defined
- [ ] Collaboration tools and processes identified
- [ ] Knowledge sharing and documentation standards established
- [ ] Training and onboarding requirements addressed

## 7. CONTINUOUS IMPROVEMENT

### 7.1 Feedback and Iteration
- [ ] Feedback collection mechanisms established
- [ ] Continuous improvement processes defined
- [ ] Lessons learned documentation and sharing planned
- [ ] Quality metrics and measurement framework established
- [ ] Regular review and update processes implemented

### 7.2 Knowledge Management
- [ ] Best practices and standards documented
- [ ] Template and process improvements identified
- [ ] Knowledge base and documentation maintained
- [ ] Training materials and resources updated
- [ ] Community of practice engagement planned

This checklist ensures that all briefs created by the Analyst provide a solid foundation for Product Manager PRD development and subsequent technical implementation, maintaining alignment with BMAD Method v4.0 principles for enterprise-scale microservices development.
