# BMAD Method Setup Script for Windows PowerShell
# Run this script to set up the BMAD development environment

param(
    [switch]$SkipNodeCheck,
    [switch]$Force,
    [switch]$Verbose
)

Write-Host "🚀 BMAD Method 4.0 Setup" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check Node.js installation
if (-not $SkipNodeCheck) {
    Write-Host "📋 Checking Node.js installation..." -ForegroundColor Yellow
    
    if (-not (Test-Command "node")) {
        Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install Node.js 16.0.0 or higher from https://nodejs.org/" -ForegroundColor Red
        exit 1
    }
    
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
    
    # Check Node.js version
    $versionNumber = $nodeVersion -replace 'v', ''
    $majorVersion = [int]($versionNumber.Split('.')[0])
    
    if ($majorVersion -lt 16) {
        Write-Host "❌ Node.js version $nodeVersion is too old. Please upgrade to 16.0.0 or higher." -ForegroundColor Red
        exit 1
    }
}

# Check npm installation
Write-Host "📋 Checking npm installation..." -ForegroundColor Yellow
if (-not (Test-Command "npm")) {
    Write-Host "❌ npm is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

$npmVersion = npm --version
Write-Host "✅ npm version: $npmVersion" -ForegroundColor Green

# Create .env file if it doesn't exist
Write-Host "📋 Setting up environment configuration..." -ForegroundColor Yellow
if (-not (Test-Path ".env") -or $Force) {
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ Created .env file from template" -ForegroundColor Green
    } else {
        Write-Host "⚠️  .env.example not found, skipping .env creation" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ .env file already exists" -ForegroundColor Green
}

# Install dependencies
Write-Host "📋 Installing dependencies..." -ForegroundColor Yellow
try {
    npm install
    Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies: $_" -ForegroundColor Red
    exit 1
}

# Run initial build
Write-Host "📋 Running initial build..." -ForegroundColor Yellow
try {
    npm run build
    Write-Host "✅ Initial build completed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed: $_" -ForegroundColor Red
    exit 1
}

# Validate setup
Write-Host "📋 Validating setup..." -ForegroundColor Yellow
try {
    npm test
    Write-Host "✅ Setup validation passed" -ForegroundColor Green
} catch {
    Write-Host "❌ Setup validation failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 BMAD Method setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Review the generated files in ./build/" -ForegroundColor White
Write-Host "2. Follow the Web Quickstart guide in README.md" -ForegroundColor White
Write-Host "3. Copy bmad-agent/ folder to your project root for IDE usage" -ForegroundColor White
Write-Host ""
Write-Host "Available commands:" -ForegroundColor Cyan
Write-Host "  npm run build        - Build web agent bundles" -ForegroundColor White
Write-Host "  npm run dev          - Clean and rebuild" -ForegroundColor White
Write-Host "  npm run deploy:web   - Prepare for web deployment" -ForegroundColor White
Write-Host "  npm test             - Validate configuration" -ForegroundColor White
