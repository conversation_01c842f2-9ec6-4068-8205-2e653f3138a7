module.exports = {
  env: {
    browser: false,
    es2021: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  rules: {
    // Code quality rules
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'no-console': 'off', // Allow console.log for build scripts
    'prefer-const': 'error',
    'no-var': 'error',
    
    // Style rules
    'indent': ['error', 2],
    'linebreak-style': ['error', 'unix'],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    
    // Best practices
    'eqeqeq': 'error',
    'curly': 'error',
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    
    // Node.js specific
    'no-process-exit': 'off', // Allow process.exit in build scripts
    'no-path-concat': 'error',
  },
  overrides: [
    {
      files: ['scripts/**/*.js'],
      rules: {
        'no-process-exit': 'off',
      },
    },
    {
      files: ['build-web-agent.js'],
      rules: {
        'no-process-exit': 'off',
      },
    },
  ],
};
