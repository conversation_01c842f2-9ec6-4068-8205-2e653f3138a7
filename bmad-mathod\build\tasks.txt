==================== START: ai-agent-orchestration-design ====================
# AI Agent Orchestration Design Task
## Multi-Agent Systems Architecture and Human-AI Collaboration Framework

## Purpose

- Design comprehensive multi-agent AI systems with sophisticated orchestration patterns
- Plan human-AI collaboration workflows and handoff procedures
- Establish AI governance, ethics, and compliance frameworks for enterprise environments
- Create scalable AI infrastructure and integration strategies for microservices architectures

Remember as you follow the upcoming instructions:

- Your orchestration design enables enterprise-scale AI capabilities
- Output will be used by Platform Engineers and AI Infrastructure teams
- Your agent coordination patterns will determine system intelligence and efficiency
- Focus on ethical AI implementation and human-centric design principles

## Instructions

### 1. AI Capability Assessment and Agent Identification

Begin with comprehensive analysis of AI opportunities and agent requirements:

#### 1A. Business Process AI Mapping
- **Process Automation Opportunities**: Identify business processes suitable for AI automation
- **Decision Support Requirements**: Map decisions that could benefit from AI assistance
- **Customer Interaction Points**: Identify customer touchpoints for conversational AI
- **Data Analysis Needs**: Understand analytical and insights generation requirements

#### 1B. Agent Type Classification
- **Conversational Agents**: Customer service, support, and interaction agents
- **Automation Agents**: Task automation, workflow execution, and process agents
- **Analytics Agents**: Data analysis, insights generation, and reporting agents
- **Orchestration Agents**: Multi-agent coordination and workflow management agents
- **Decision Agents**: Automated decision-making and recommendation agents
- **Monitoring Agents**: System monitoring, anomaly detection, and alerting agents

#### 1C. AI Capability Requirements
- **Natural Language Processing**: Text understanding, generation, and conversation capabilities
- **Computer Vision**: Image and video analysis and processing requirements
- **Machine Learning**: Predictive modeling and pattern recognition needs
- **Knowledge Management**: Information retrieval and knowledge base integration
- **Reasoning and Planning**: Complex reasoning and multi-step planning capabilities

### 2. Multi-Agent Architecture Design

Design sophisticated multi-agent system architecture:

#### 2A. Agent Hierarchy and Relationships
- **Master Orchestrator Agents**: High-level coordination and workflow management
- **Specialized Worker Agents**: Domain-specific task execution and processing
- **Coordination Agents**: Inter-agent communication and synchronization
- **Monitoring and Control Agents**: System health and performance monitoring

#### 2B. Agent Communication Patterns
- **Message Passing**: Asynchronous message-based communication between agents
- **Event-Driven Communication**: Event publishing and subscription patterns
- **Shared Memory**: Shared state and knowledge base access patterns
- **API-Based Integration**: RESTful and GraphQL API communication protocols

#### 2C. Agent Coordination Mechanisms
- **Workflow Orchestration**: Sequential and parallel workflow execution patterns
- **Task Distribution**: Dynamic task allocation and load balancing strategies
- **Conflict Resolution**: Handling conflicting agent decisions and priorities
- **Resource Management**: Shared resource allocation and optimization

### 3. Human-AI Collaboration Framework

Design comprehensive human-AI collaboration patterns:

#### 3A. Collaboration Models
- **Human-in-the-Loop**: Direct human involvement in AI decision-making processes
- **Human-on-the-Loop**: Human oversight and monitoring of AI operations
- **Human-out-of-the-Loop**: Fully autonomous AI operation with human escalation
- **Hybrid Collaboration**: Dynamic switching between collaboration modes

#### 3B. Handoff Procedures
- **Agent-to-Human Escalation**: Criteria and procedures for escalating to human experts
- **Human-to-Agent Delegation**: Procedures for delegating tasks to AI agents
- **Context Preservation**: Maintaining context and state during handoffs
- **Knowledge Transfer**: Sharing insights and learning between humans and agents

#### 3C. User Experience Design
- **Conversational Interfaces**: Natural language interaction design and patterns
- **Visual Interfaces**: Dashboard and visualization design for AI insights
- **Mobile Interfaces**: Mobile-optimized AI interaction patterns
- **Accessibility**: Ensuring AI interfaces are accessible to all users

### 4. AI Infrastructure and Platform Integration

Plan AI infrastructure integration with microservices architecture:

#### 4A. AI Platform Architecture
- **Model Serving Infrastructure**: Scalable model deployment and serving platforms
- **Vector Database Integration**: Embedding storage and similarity search capabilities
- **Knowledge Graph Systems**: Structured knowledge representation and reasoning
- **Real-time Processing**: Stream processing for real-time AI capabilities

#### 4B. Microservices Integration Patterns
- **AI Service Mesh**: Integration of AI agents with service mesh architecture
- **Event-Driven AI**: AI agents as event consumers and producers in distributed systems
- **API Gateway Integration**: AI service exposure through API gateways
- **Service Discovery**: AI agent registration and discovery mechanisms

#### 4C. Data Pipeline Integration
- **Training Data Pipelines**: Data collection and preparation for model training
- **Inference Data Flows**: Real-time data flows for AI inference and decision-making
- **Feedback Loops**: Capturing user feedback and model performance data
- **Data Governance**: Ensuring data quality, privacy, and compliance

### 5. AI Governance and Ethics Framework

Establish comprehensive AI governance and ethical guidelines:

#### 5A. Ethical AI Principles
- **Fairness and Bias Mitigation**: Ensuring AI systems are fair and unbiased
- **Transparency and Explainability**: Making AI decisions transparent and explainable
- **Accountability and Responsibility**: Establishing clear accountability for AI decisions
- **Privacy and Data Protection**: Protecting user privacy and personal data

#### 5B. AI Risk Management
- **Risk Assessment Framework**: Systematic assessment of AI-related risks
- **Mitigation Strategies**: Strategies for mitigating identified risks
- **Monitoring and Detection**: Continuous monitoring for bias, drift, and anomalies
- **Incident Response**: Procedures for responding to AI-related incidents

#### 5C. Compliance and Regulation
- **Regulatory Compliance**: Ensuring compliance with AI regulations and standards
- **Audit and Documentation**: Comprehensive documentation and audit trails
- **Governance Committees**: Establishing AI governance and oversight committees
- **Policy Development**: Developing AI policies and guidelines

### 6. Performance and Scalability Planning

Design for enterprise-scale performance and scalability:

#### 6A. Performance Optimization
- **Model Optimization**: Optimizing AI models for performance and efficiency
- **Caching Strategies**: Intelligent caching of AI results and intermediate computations
- **Load Balancing**: Distributing AI workloads across multiple instances
- **Resource Allocation**: Dynamic resource allocation based on demand

#### 6B. Scalability Architecture
- **Horizontal Scaling**: Scaling AI agents across multiple nodes and clusters
- **Auto-scaling Policies**: Automatic scaling based on load and performance metrics
- **Multi-region Deployment**: Deploying AI agents across multiple geographic regions
- **Edge Computing**: Deploying AI capabilities at the edge for low-latency requirements

#### 6C. Monitoring and Observability
- **Performance Metrics**: Comprehensive metrics for AI agent performance
- **Business Metrics**: Tracking business impact and value creation
- **Technical Metrics**: Monitoring technical performance and resource utilization
- **User Experience Metrics**: Measuring user satisfaction and interaction quality

### 7. Security and Privacy Implementation

Implement comprehensive security and privacy controls:

#### 7A. AI Security Architecture
- **Authentication and Authorization**: Secure access control for AI agents and users
- **Encryption**: Protecting AI models, data, and communications
- **Threat Detection**: Detecting and responding to AI-specific security threats
- **Secure Development**: Secure AI development and deployment practices

#### 7B. Privacy Protection
- **Data Minimization**: Collecting and processing only necessary data
- **Anonymization**: Protecting user identity and personal information
- **Consent Management**: Managing user consent for AI processing
- **Right to Explanation**: Providing explanations for AI decisions when required

#### 7C. Model Security
- **Model Protection**: Protecting AI models from theft and reverse engineering
- **Adversarial Defense**: Defending against adversarial attacks on AI models
- **Model Versioning**: Secure versioning and deployment of AI models
- **Access Control**: Controlling access to AI models and training data

### 8. Implementation Strategy and Roadmap

Plan phased implementation of AI agent orchestration:

#### 8A. Implementation Phases
- **Phase 1 - Foundation**: Establish AI infrastructure and basic agent capabilities
- **Phase 2 - Core Agents**: Implement core AI agents and basic orchestration
- **Phase 3 - Advanced Orchestration**: Implement sophisticated multi-agent coordination
- **Phase 4 - Optimization**: Optimize performance, scaling, and user experience

#### 8B. Technology Selection
- **AI Frameworks**: Select appropriate AI frameworks and platforms (LangChain, LangGraph, etc.)
- **Model Providers**: Choose model providers and deployment strategies
- **Infrastructure Platforms**: Select cloud platforms and infrastructure services
- **Integration Tools**: Choose tools for integration with existing systems

#### 8C. Change Management
- **Training Programs**: Training users and developers on AI capabilities
- **Adoption Strategy**: Strategies for driving AI adoption across the organization
- **Communication Plan**: Communicating AI capabilities and benefits
- **Feedback Mechanisms**: Collecting and incorporating user feedback

## Deliverables

### Primary Deliverable
Comprehensive AI Agent Orchestration Design including:
- Multi-agent system architecture with coordination patterns
- Human-AI collaboration framework with handoff procedures
- AI infrastructure integration strategy with microservices architecture
- AI governance and ethics framework with compliance procedures
- Performance and scalability plan with monitoring strategies
- Security and privacy implementation with protection mechanisms
- Implementation roadmap with phased approach and technology selection

### Secondary Deliverables
- Agent specification documents for each identified agent type
- API specifications for agent communication and integration
- User experience design for human-AI interaction
- Training and adoption materials for users and developers
- Monitoring and alerting configuration for AI operations

## Success Criteria

- Multi-agent system enables sophisticated AI capabilities with efficient coordination
- Human-AI collaboration enhances human capabilities while maintaining appropriate oversight
- AI infrastructure integrates seamlessly with microservices architecture
- AI governance framework ensures ethical and compliant AI operations
- Performance and scalability meet enterprise requirements
- Security and privacy controls protect users and organizational data
- Implementation roadmap provides clear path to AI-enabled organization

## Validation Checklist

- [ ] Multi-agent architecture designed with clear coordination patterns
- [ ] Human-AI collaboration framework addresses all interaction scenarios
- [ ] AI infrastructure integration aligns with microservices architecture
- [ ] AI governance and ethics framework comprehensive and implementable
- [ ] Performance and scalability requirements addressed with monitoring
- [ ] Security and privacy controls comprehensive and effective
- [ ] Implementation roadmap realistic with clear phases and milestones
- [ ] Technology selection appropriate for organizational needs and constraints

==================== END: ai-agent-orchestration-design ====================


==================== START: api-gateway-strategy-design ====================
# API Gateway Strategy Design Task

## Objective
Design a comprehensive API Gateway strategy that provides unified access, security, and management for microfrontend-to-backend service communication while enabling independent team operations and scalable architecture.

## Context
You are designing an API Gateway that serves as the central entry point for all frontend-to-backend communication in a microfrontend architecture, providing security, routing, monitoring, and protocol translation capabilities.

## Prerequisites
- Review microfrontend architecture and service decomposition
- Understand backend service landscape and API contracts
- Assess security and compliance requirements
- Identify performance and scalability requirements
- Review existing infrastructure and technology constraints

## Task Instructions

### 1. Gateway Architecture Strategy

#### Gateway Pattern Selection
Choose the appropriate gateway pattern:

```markdown
# API Gateway Architecture Patterns

## Centralized Gateway Pattern
**Use Cases**:
- Unified security and authentication
- Cross-cutting concerns (logging, monitoring, rate limiting)
- Protocol translation and request/response transformation
- Centralized API management and governance

**Benefits**:
- Single point of control for security policies
- Simplified client configuration
- Centralized monitoring and analytics
- Consistent API standards enforcement

**Challenges**:
- Potential single point of failure
- Bottleneck for high-traffic scenarios
- Coordination required for gateway changes

## Distributed Gateway Pattern (Backend for Frontend)
**Use Cases**:
- Microfrontend-specific API aggregation
- Domain-specific protocol optimization
- Team autonomy for API design
- Reduced coupling between frontend and backend teams

**Benefits**:
- Team autonomy and independence
- Optimized APIs for specific frontend needs
- Reduced blast radius of failures
- Technology diversity support

**Challenges**:
- Increased operational complexity
- Potential code duplication
- Inconsistent security implementations

## Hybrid Gateway Pattern (Recommended)
**Architecture**:
```
Internet → CDN → L7 Load Balancer
    ↓
Central API Gateway (Security, Routing, Monitoring)
    ↓
Domain-Specific BFF Services
    ↓
Backend Microservices
```

**Implementation Strategy**:
- Central gateway for cross-cutting concerns
- Domain-specific BFF services for aggregation
- Direct service access for high-performance scenarios
```

### 2. Gateway Technology Selection

#### Technology Evaluation Matrix
```markdown
# Gateway Technology Comparison

## Kong Gateway
**Strengths**:
- Enterprise-grade features and support
- Extensive plugin ecosystem
- Kubernetes-native deployment
- Strong performance and scalability
- Comprehensive API management

**Use Cases**:
- Enterprise environments requiring compliance
- Complex routing and transformation needs
- Extensive third-party integrations
- High-performance requirements

## NGINX Plus / NGINX Ingress
**Strengths**:
- High performance and low latency
- Mature and battle-tested
- Flexible configuration
- Strong load balancing capabilities
- Cost-effective solution

**Use Cases**:
- High-traffic applications
- Simple routing and load balancing
- Cost-sensitive deployments
- Performance-critical scenarios

## Istio Gateway
**Strengths**:
- Service mesh integration
- Advanced traffic management
- Strong security features
- Observability and monitoring
- Cloud-native architecture

**Use Cases**:
- Service mesh environments
- Complex traffic routing needs
- Strong security requirements
- Microservices architectures

## AWS API Gateway / Azure API Management
**Strengths**:
- Fully managed service
- Integrated with cloud services
- Built-in monitoring and analytics
- Automatic scaling
- Pay-per-use pricing

**Use Cases**:
- Cloud-native applications
- Serverless architectures
- Rapid development and deployment
- Minimal operational overhead
```

### 3. Routing and Traffic Management

#### Routing Strategy Design
```yaml
# Kong routing configuration example
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: user-bff
  annotations:
    konghq.com/tags: "microfrontend,user-domain"
spec:
  host: user-bff-service
  port: 8080
  protocol: http
  path: /api/v1
  connect_timeout: 5000
  write_timeout: 10000
  read_timeout: 10000
  retries: 3
---
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: user-api-routes
spec:
  service: user-bff
  protocols:
  - http
  - https
  hosts:
  - api.company.com
  paths:
  - /api/users
  - /api/profiles
  - /api/preferences
  strip_path: true
  preserve_host: false
  regex_priority: 0
  path_handling: v1
```

#### Advanced Routing Patterns
```typescript
// Dynamic routing configuration
interface RoutingRule {
  id: string;
  pattern: string;
  destination: ServiceDestination;
  conditions: RoutingCondition[];
  transformations: RequestTransformation[];
  rateLimit?: RateLimitConfig;
  authentication?: AuthenticationConfig;
}

interface ServiceDestination {
  service: string;
  version?: string;
  weight?: number;
  timeout?: number;
  retries?: number;
}

interface RoutingCondition {
  type: 'header' | 'query' | 'path' | 'method' | 'user-agent';
  key: string;
  operator: 'equals' | 'contains' | 'regex' | 'exists';
  value: string;
}

// Example routing rules
const routingRules: RoutingRule[] = [
  {
    id: 'user-mobile-api',
    pattern: '/api/users/*',
    destination: {
      service: 'user-mobile-bff',
      version: 'v2',
      timeout: 5000,
    },
    conditions: [
      {
        type: 'header',
        key: 'User-Agent',
        operator: 'contains',
        value: 'Mobile',
      },
    ],
    transformations: [
      {
        type: 'request-header',
        action: 'add',
        key: 'X-Client-Type',
        value: 'mobile',
      },
    ],
  },
  {
    id: 'user-web-api',
    pattern: '/api/users/*',
    destination: {
      service: 'user-web-bff',
      version: 'v1',
      timeout: 3000,
    },
    conditions: [],
    transformations: [],
  },
];
```

### 4. Security Implementation

#### Authentication and Authorization Strategy
```yaml
# OAuth 2.0 / OIDC Configuration
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: oidc-authentication
plugin: openid-connect
config:
  issuer: "https://auth.company.com/realms/company"
  client_id: "api-gateway"
  client_secret: "gateway-client-secret"
  redirect_uri: "https://app.company.com/auth/callback"
  scope: "openid profile email"
  response_type: "code"
  ssl_verify: true
  session_secret: "session-encryption-key"
  session_storage: "redis"
  session_redis_host: "redis-session-store"
  session_redis_port: 6379
  session_redis_auth: "redis-password"
  recovery_page_path: "/auth/error"
  logout_path: "/auth/logout"
  post_logout_redirect_uri: "https://app.company.com"
---
# JWT Validation for API access
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: jwt-validation
plugin: jwt
config:
  uri_param_names:
  - token
  header_names:
  - Authorization
  claims_to_verify:
  - exp
  - iat
  - iss
  - aud
  key_claim_name: iss
  secret_is_base64: false
  run_on_preflight: true
  maximum_expiration: 3600
```

#### Rate Limiting and DDoS Protection
```yaml
# Advanced Rate Limiting
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: advanced-rate-limiting
plugin: rate-limiting-advanced
config:
  limit:
  - 1000  # requests per window
  window_size:
  - 3600  # 1 hour window
  identifier: consumer
  sync_rate: 10
  strategy: redis
  redis:
    host: redis-rate-limit
    port: 6379
    database: 1
    timeout: 2000
  hide_client_headers: false
  header_name: "X-RateLimit-Limit"
  namespace: "rate_limit"
---
# DDoS Protection
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: ddos-protection
plugin: bot-detection
config:
  whitelist: []
  blacklist:
  - "BadBot"
  - "MaliciousScanner"
  rules:
  - name: "suspicious-user-agent"
    condition: "headers.user_agent matches '.*bot.*'"
    action: "deny"
  - name: "high-request-rate"
    condition: "rate > 100"
    action: "delay"
    delay: 5000
```

### 5. Performance Optimization

#### Caching Strategy
```yaml
# Response Caching
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: response-caching
plugin: proxy-cache
config:
  response_code:
  - 200
  - 301
  - 404
  request_method:
  - GET
  - HEAD
  content_type:
  - text/plain
  - application/json
  - text/html
  cache_ttl: 300
  cache_control: true
  storage_ttl: 3600
  strategy: redis
  redis:
    host: redis-cache
    port: 6379
    database: 2
    timeout: 2000
  vary_headers:
  - Accept-Encoding
  - Accept-Language
  vary_nginx_variables: []
```

#### Load Balancing Configuration
```yaml
# Upstream Load Balancing
apiVersion: configuration.konghq.com/v1
kind: KongUpstream
metadata:
  name: user-service-cluster
spec:
  name: user-service
  algorithm: consistent-hashing
  hash_on: header
  hash_on_header: X-User-ID
  hash_fallback: ip
  slots: 10000
  healthchecks:
    active:
      type: http
      http_path: /health
      https_verify_certificate: false
      healthy:
        interval: 10
        http_statuses:
        - 200
        - 302
        successes: 3
      unhealthy:
        interval: 10
        http_statuses:
        - 429
        - 500
        - 502
        - 503
        - 504
        http_failures: 3
        timeouts: 3
    passive:
      type: http
      healthy:
        http_statuses:
        - 200
        - 201
        - 202
        - 203
        - 204
        - 205
        - 206
        - 300
        - 301
        - 302
        - 303
        - 304
        - 307
        - 308
        successes: 3
      unhealthy:
        http_statuses:
        - 429
        - 500
        - 502
        - 503
        - 504
        http_failures: 3
        timeouts: 3
```

### 6. Monitoring and Observability

#### Metrics Collection
```yaml
# Prometheus Metrics
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: prometheus-metrics
plugin: prometheus
config:
  per_consumer: true
  status_code_metrics: true
  latency_metrics: true
  bandwidth_metrics: true
  upstream_health_metrics: true
  custom_metrics:
  - name: "api_requests_total"
    stat_type: "counter"
    labels:
    - "service"
    - "route"
    - "method"
    - "status"
  - name: "api_request_duration_seconds"
    stat_type: "histogram"
    labels:
    - "service"
    - "route"
    - "method"
```

#### Distributed Tracing
```yaml
# Jaeger Tracing
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: jaeger-tracing
plugin: zipkin
config:
  http_endpoint: "http://jaeger-collector:14268/api/traces"
  sample_ratio: 0.1
  include_credential: true
  traceid_byte_count: 16
  header_type: jaeger
  default_header_type: jaeger
  tags:
    service: "api-gateway"
    environment: "production"
    version: "v1.0.0"
```

### 7. Error Handling and Resilience

#### Circuit Breaker Implementation
```typescript
// Circuit breaker configuration
interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  expectedExceptionTypes: string[];
}

class APIGatewayCircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(private config: CircuitBreakerConfig) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime >= this.config.recoveryTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN - service unavailable');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(error: any): void {
    if (this.isExpectedException(error)) {
      return; // Don't count expected exceptions as failures
    }
    
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.config.failureThreshold) {
      this.state = 'OPEN';
    }
  }
  
  private isExpectedException(error: any): boolean {
    return this.config.expectedExceptionTypes.some(type => 
      error.name === type || error.constructor.name === type
    );
  }
}
```

#### Retry and Timeout Strategy
```yaml
# Request Timeout and Retry
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: resilient-service
spec:
  host: backend-service
  port: 8080
  protocol: http
  connect_timeout: 5000
  write_timeout: 10000
  read_timeout: 10000
  retries: 3
  tags:
  - "resilient"
  - "production"
---
# Retry Plugin Configuration
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: request-retry
plugin: request-termination
config:
  status_code: 503
  content_type: "application/json"
  body: '{"error": "Service temporarily unavailable", "retry_after": 30}'
  trigger: "X-Retry-Required"
```

### 8. Development and Testing Strategy

#### Gateway Testing Framework
```typescript
// Gateway integration tests
import { APIGatewayTestClient } from './test-utils/gateway-client';
import { MockBackendService } from './test-utils/mock-service';

describe('API Gateway Integration Tests', () => {
  let gatewayClient: APIGatewayTestClient;
  let mockUserService: MockBackendService;
  
  beforeAll(async () => {
    gatewayClient = new APIGatewayTestClient('http://localhost:8000');
    mockUserService = new MockBackendService('user-service', 8081);
    await mockUserService.start();
  });
  
  afterAll(async () => {
    await mockUserService.stop();
  });
  
  describe('Authentication', () => {
    it('should reject requests without valid JWT', async () => {
      const response = await gatewayClient.get('/api/users/profile');
      expect(response.status).toBe(401);
      expect(response.data.error).toBe('Unauthorized');
    });
    
    it('should allow requests with valid JWT', async () => {
      const token = await gatewayClient.authenticate('<EMAIL>', 'password');
      const response = await gatewayClient.get('/api/users/profile', {
        headers: { Authorization: `Bearer ${token}` }
      });
      expect(response.status).toBe(200);
    });
  });
  
  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const token = await gatewayClient.authenticate('<EMAIL>', 'password');
      
      // Make requests up to the limit
      for (let i = 0; i < 100; i++) {
        const response = await gatewayClient.get('/api/users/profile', {
          headers: { Authorization: `Bearer ${token}` }
        });
        expect(response.status).toBe(200);
      }
      
      // Next request should be rate limited
      const response = await gatewayClient.get('/api/users/profile', {
        headers: { Authorization: `Bearer ${token}` }
      });
      expect(response.status).toBe(429);
    });
  });
  
  describe('Circuit Breaker', () => {
    it('should open circuit when service fails', async () => {
      const token = await gatewayClient.authenticate('<EMAIL>', 'password');
      
      // Simulate service failures
      mockUserService.simulateFailure(500, 5); // 5 consecutive failures
      
      // Make requests to trigger circuit breaker
      for (let i = 0; i < 5; i++) {
        try {
          await gatewayClient.get('/api/users/profile', {
            headers: { Authorization: `Bearer ${token}` }
          });
        } catch (error) {
          // Expected failures
        }
      }
      
      // Circuit should be open now
      const response = await gatewayClient.get('/api/users/profile', {
        headers: { Authorization: `Bearer ${token}` }
      });
      expect(response.status).toBe(503);
      expect(response.data.error).toContain('Circuit breaker');
    });
  });
});
```

## Output Requirements
1. **Gateway Architecture Document**: Comprehensive strategy and design
2. **Technology Selection Rationale**: Detailed comparison and selection criteria
3. **Security Implementation Plan**: Authentication, authorization, and protection strategies
4. **Performance Optimization Strategy**: Caching, load balancing, and optimization techniques
5. **Monitoring and Observability Setup**: Metrics, logging, and tracing configuration
6. **Testing Framework**: Integration and performance testing approach

## Quality Checklist
- [ ] Gateway architecture supports microfrontend independence
- [ ] Security implementation meets compliance requirements
- [ ] Performance optimization strategies are comprehensive
- [ ] Monitoring and observability provide full visibility
- [ ] Error handling and resilience patterns are robust
- [ ] Testing strategy ensures reliability and performance
- [ ] Documentation is complete and maintainable

## Success Criteria
The API Gateway strategy is successful when:
1. **Security**: Robust authentication and authorization
2. **Performance**: Low latency and high throughput
3. **Reliability**: High availability with graceful degradation
4. **Scalability**: Handles growth in traffic and services
5. **Observability**: Comprehensive monitoring and debugging
6. **Maintainability**: Easy to configure and update

## Notes
- Consider starting with a simple gateway configuration and evolving
- Ensure proper load testing before production deployment
- Establish clear governance for gateway configuration changes
- Monitor performance impact and optimize based on real usage patterns

==================== END: api-gateway-strategy-design ====================


==================== START: checklist-mappings ====================
architect-checklist:
  checklist_file: bmad-agent/checklists/architect-checklist.md
  required_docs:
    - architecture.md
  default_locations:
    - docs/architecture.md

frontend-architecture-checklist:
  checklist_file: bmad-agent/checklists/frontend-architecture-checklist.md
  required_docs:
    - frontend-architecture.md
  default_locations:
    - docs/frontend-architecture.md
    - docs/fe-architecture.md

pm-checklist:
  checklist_file: bmad-agent/checklists/pm-checklist.md
  required_docs:
    - prd.md
  default_locations:
    - docs/prd.md

po-master-checklist:
  checklist_file: bmad-agent/checklists/po-master-checklist.md
  required_docs:
    - prd.md
    - architecture.md
  optional_docs:
    - frontend-architecture.md
  default_locations:
    - docs/prd.md
    - docs/frontend-architecture.md
    - docs/architecture.md

story-draft-checklist:
  checklist_file: bmad-agent/checklists/story-draft-checklist.md
  required_docs:
    - story.md
  default_locations:
    - docs/stories/*.md

story-dod-checklist:
  checklist_file: bmad-agent/checklists/story-dod-checklist.md
  required_docs:
    - story.md
  default_locations:
    - docs/stories/*.md

# Modern Microservices Checklists
microservices-architecture-checklist:
  checklist_file: bmad-agent/checklists/microservices-architecture-checklist.md
  required_docs:
    - architecture.md
    - service-integration-contracts.md
  default_locations:
    - docs/architecture.md
    - docs/service-integration-contracts.md

service-mesh-checklist:
  checklist_file: bmad-agent/checklists/service-mesh-checklist.md
  required_docs:
    - architecture.md
    - service-mesh-config.md
  default_locations:
    - docs/architecture.md
    - docs/service-mesh-config.md

platform-engineering-checklist:
  checklist_file: bmad-agent/checklists/platform-engineering-checklist.md
  required_docs:
    - platform-strategy.md
    - architecture.md
  default_locations:
    - docs/platform-strategy.md
    - docs/architecture.md

ai-orchestration-checklist:
  checklist_file: bmad-agent/checklists/ai-orchestration-checklist.md
  required_docs:
    - ai-architecture.md
    - ai-governance.md
  default_locations:
    - docs/ai-architecture.md
    - docs/ai-governance.md

ai-integration-checklist:
  checklist_file: bmad-agent/checklists/ai-integration-checklist.md
  required_docs:
    - ai-architecture.md
  default_locations:
    - docs/ai-architecture.md

platform-architect-checklist:
  checklist_file: bmad-agent/checklists/platform-architect-checklist.md
  required_docs:
    - platform-strategy.md
    - architecture.md
  default_locations:
    - docs/platform-strategy.md
    - docs/architecture.md

# Microfrontend-Specific Checklists
microfrontend-architecture-checklist:
  checklist_file: bmad-agent/checklists/microfrontend-architecture-checklist.md
  required_docs:
    - microfrontend-architecture.md
    - design-system-integration.md
  default_locations:
    - docs/microfrontend-architecture.md
    - docs/design-system-integration.md

frontend-service-integration-checklist:
  checklist_file: bmad-agent/checklists/frontend-service-integration-checklist.md
  required_docs:
    - frontend-service-contracts.md
    - api-integration-guide.md
  default_locations:
    - docs/frontend-service-contracts.md
    - docs/api-integration-guide.md

==================== END: checklist-mappings ====================


==================== START: checklist-run-task ====================
# Checklist Validation Task

This task provides instructions for validating documentation against checklists. The agent should follow these instructions to ensure thorough and systematic validation of documents.

## Context

The BMAD Method uses various checklists to ensure quality and completeness of different artifacts. The mapping between checklists and their required documents is defined in `checklist-mappings`. This allows for easy addition of new checklists without modifying this task.

## Instructions

1. **Initial Assessment**

   - Check `checklist-mappings` for available checklists
   - If user provides a checklist name:
     - Look for exact match in checklist-mappings.yml
     - If no exact match, try fuzzy matching (e.g. "architecture checklist" -> "architect-checklist")
     - If multiple matches found, ask user to clarify
     - Once matched, use the checklist_file path from the mapping
   - If no checklist specified:
     - Ask the user which checklist they want to use
     - Present available options from checklist-mappings.yml
   - Confirm if they want to work through the checklist:
     - Section by section (interactive mode)
     - All at once (YOLO mode)

2. **Document Location**

   - Look up the required documents and default locations in `checklist-mappings`
   - For each required document:
     - Check all default locations specified in the mapping
     - If not found, ask the user for the document location
   - Verify all required documents are accessible

3. **Checklist Processing**

   If in interactive mode:

   - Work through each section of the checklist one at a time
   - For each section:
     - Review all items in the section
     - Check each item against the relevant documentation
     - Present findings for that section
     - Get user confirmation before proceeding to next section

   If in YOLO mode:

   - Process all sections at once
   - Create a comprehensive report of all findings
   - Present the complete analysis to the user

4. **Validation Approach**

   For each checklist item:

   - Read and understand the requirement
   - Look for evidence in the documentation that satisfies the requirement
   - Consider both explicit mentions and implicit coverage
   - Mark items as:
     - ✅ PASS: Requirement clearly met
     - ❌ FAIL: Requirement not met or insufficient coverage
     - ⚠️ PARTIAL: Some aspects covered but needs improvement
     - N/A: Not applicable to this case

5. **Section Analysis**

   For each section:

   - Calculate pass rate
   - Identify common themes in failed items
   - Provide specific recommendations for improvement
   - In interactive mode, discuss findings with user
   - Document any user decisions or explanations

6. **Final Report**

   Prepare a summary that includes:

   - Overall checklist completion status
   - Pass rates by section
   - List of failed items with context
   - Specific recommendations for improvement
   - Any sections or items marked as N/A with justification

## Special Considerations

1. **Architecture Checklist**

   - Focus on technical completeness and clarity
   - Verify all system components are addressed
   - Check for security and scalability considerations
   - Ensure deployment and operational aspects are covered

2. **Frontend Architecture Checklist**

   - Validate UI/UX specifications
   - Check component structure and organization
   - Verify state management approach
   - Ensure responsive design considerations

3. **PM Checklist**

   - Focus on product requirements clarity
   - Verify user stories and acceptance criteria
   - Check market and user research coverage
   - Ensure technical feasibility is addressed

4. **Story Checklists**
   - Verify clear acceptance criteria
   - Check for technical context and dependencies
   - Ensure testability is addressed
   - Validate user value is clearly stated

## Success Criteria

The checklist validation is complete when:

1. All applicable items have been assessed
2. Clear pass/fail status for each item
3. Specific recommendations provided for failed items
4. User has reviewed and acknowledged findings
5. Final report documents all decisions and rationales

## Example Interaction

Agent: "Let me check the available checklists... According to checklist-mappings.yml, we have several options. Which would you like to use?"

User: "The architect checklist"

Agent: "Would you like to work through it section by section (interactive) or get a complete analysis all at once (YOLO mode)?"

User: "Interactive please"

Agent: "According to the mappings, I need to check for architecture.md. The default location is docs/architecture.md. Should I look there?"

[Continue interaction based on user responses...]

==================== END: checklist-run-task ====================


==================== START: core-dump ====================
# Core Dump Task

## Purpose

To create a concise memory recording file (`.ai/core-dump-n.md`) that captures the essential context of the current agent session, enabling seamless continuation of work in future agent sessions. This task ensures persistent context across agent conversations while maintaining minimal token usage for efficient context loading.

## Inputs for this Task

- Current session conversation history and accomplishments
- Files created, modified, or deleted during the session
- Key decisions made and procedures followed
- Current project state and next logical steps
- User requests and agent responses that shaped the session

## Task Execution Instructions

### 0. Check Existing Core Dump

Before proceeding, check if `.ai/core-dump.md` already exists:

- If file exists, ask user: "Core dump file exists. Should I: 1. Overwrite, 2. Update, 3. Append or 4. Create new?"
- **Overwrite**: Replace entire file with new content
- **Update**: Merge new session info with existing content, updating relevant sections
- **Append**: Add new session as a separate entry while preserving existing content
- **Create New**: Create a new file, appending the next possible -# to the file, such as core-dump-3.md if 1 and 2 already exist.
- If file doesn't exist, proceed with creation of `core-dump-1.md`

### 1. Analyze Session Context

- Review the entire conversation to identify key accomplishments
- Note any specific tasks, procedures, or workflows that were executed
- Identify important decisions made or problems solved
- Capture the user's working style and preferences observed during the session

### 2. Document What Was Accomplished

- **Primary Actions**: List the main tasks completed concisely
- **Story Progress**: For story work, use format "Tasks Complete: 1-6, 8. Next Task Pending: 7, 9"
- **Problem Solving**: Document any challenges encountered and how they were resolved
- **User Communications**: Summarize key user requests, preferences, and discussion points

### 3. Record File System Changes (Concise Format)

- **Files Created**: `filename.ext` (brief purpose/size)
- **Files Modified**: `filename.ext` (what changed)
- **Files Deleted**: `filename.ext` (why removed)
- Focus on essential details, avoid verbose descriptions

### 4. Capture Current Project State

- **Project Progress**: Where the project stands after this session
- **Current Issues**: Any blockers or problems that need resolution
- **Next Logical Steps**: What would be the natural next actions to take

### 5. Create/Update Core Dump File

Based on user's choice from step 0, handle the file accordingly:

### 6. Optimize for Minimal Context

- Keep descriptions concise but informative
- Use abbreviated formats where possible (file sizes, task numbers)
- Focus on actionable information rather than detailed explanations
- Avoid redundant information that can be found in project documentation
- Prioritize information that would be lost without this recording
- Ensure the file can be quickly scanned and understood

### 7. Validate Completeness

- Verify all significant session activities are captured
- Ensure a future agent could understand the current state
- Check that file changes are accurately recorded
- Confirm next steps are clear and actionable
- Verify user communication style and preferences are noted

==================== END: core-dump ====================


==================== START: correct-course ====================
# Correct Course Task

## Purpose

- Guide a structured response to a change trigger using the `change-checklist`.
- Analyze the impacts of the change on epics, project artifacts, and the MVP, guided by the checklist's structure.
- Explore potential solutions (e.g., adjust scope, rollback elements, rescope features) as prompted by the checklist.
- Draft specific, actionable proposed updates to any affected project artifacts (e.g., epics, user stories, PRD sections, architecture document sections) based on the analysis.
- Produce a consolidated "Sprint Change Proposal" document that contains the impact analysis and the clearly drafted proposed edits for user review and approval.
- Ensure a clear handoff path if the nature of the changes necessitates fundamental replanning by other core agents (like PM or Architect).

## Instructions

### 1. Initial Setup & Mode Selection

- **Acknowledge Task & Inputs:**
  - Confirm with the user that the "Correct Course Task" (Change Navigation & Integration) is being initiated.
  - Verify the change trigger and ensure you have the user's initial explanation of the issue and its perceived impact.
  - Confirm access to all relevant project artifacts (e.g., PRD, Epics/Stories, Architecture Documents, UI/UX Specifications) and, critically, the `change-checklist` (e.g., `change-checklist`).
- **Establish Interaction Mode:**
  - Ask the user their preferred interaction mode for this task:
    - **"Incrementally (Default & Recommended):** Shall we work through the `change-checklist` section by section, discussing findings and collaboratively drafting proposed changes for each relevant part before moving to the next? This allows for detailed, step-by-step refinement."
    - **"YOLO Mode (Batch Processing):** Or, would you prefer I conduct a more batched analysis based on the checklist and then present a consolidated set of findings and proposed changes for a broader review? This can be quicker for initial assessment but might require more extensive review of the combined proposals."
  - Request the user to select their preferred mode.
  - Once the user chooses, confirm the selected mode (e.g., "Okay, we will proceed in Incremental mode."). This chosen mode will govern how subsequent steps in this task are executed.
- **Explain Process:** Briefly inform the user: "We will now use the `change-checklist` to analyze the change and draft proposed updates. I will guide you through the checklist items based on our chosen interaction mode."
  <rule>When asking multiple questions or presenting multiple points for user input at once, number them clearly (e.g., 1., 2a., 2b.) to make it easier for the user to provide specific responses.</rule>

### 2. Execute Checklist Analysis (Iteratively or Batched, per Interaction Mode)

- Systematically work through Sections 1-4 of the `change-checklist` (typically covering Change Context, Epic/Story Impact Analysis, Artifact Conflict Resolution, and Path Evaluation/Recommendation).
- For each checklist item or logical group of items (depending on interaction mode):
  - Present the relevant prompt(s) or considerations from the checklist to the user.
  - Request necessary information and actively analyze the relevant project artifacts (PRD, epics, architecture documents, story history, etc.) to assess the impact.
  - Discuss your findings for each item with the user.
  - Record the status of each checklist item (e.g., `[x] Addressed`, `[N/A]`, `[!] Further Action Needed`) and any pertinent notes or decisions.
  - Collaboratively agree on the "Recommended Path Forward" as prompted by Section 4 of the checklist.

### 3. Draft Proposed Changes (Iteratively or Batched)

- Based on the completed checklist analysis (Sections 1-4) and the agreed "Recommended Path Forward" (excluding scenarios requiring fundamental replans that would necessitate immediate handoff to PM/Architect):
  - Identify the specific project artifacts that require updates (e.g., specific epics, user stories, PRD sections, architecture document components, diagrams).
  - **Draft the proposed changes directly and explicitly for each identified artifact.** Examples include:
    - Revising user story text, acceptance criteria, or priority.
    - Adding, removing, reordering, or splitting user stories within epics.
    - Proposing modified architecture diagram snippets (e.g., providing an updated Mermaid diagram block or a clear textual description of the change to an existing diagram).
    - Updating technology lists, configuration details, or specific sections within the PRD or architecture documents.
    - Drafting new, small supporting artifacts if necessary (e.g., a brief addendum for a specific decision).
  - If in "Incremental Mode," discuss and refine these proposed edits for each artifact or small group of related artifacts with the user as they are drafted.
  - If in "YOLO Mode," compile all drafted edits for presentation in the next step.

### 4. Generate "Sprint Change Proposal" with Edits

- Synthesize the complete `change-checklist` analysis (covering findings from Sections 1-4) and all the agreed-upon proposed edits (from Instruction 3) into a single document titled "Sprint Change Proposal." This proposal should align with the structure suggested by Section 5 of the `change-checklist` (Proposal Components).
- The proposal must clearly present:
  - **Analysis Summary:** A concise overview of the original issue, its analyzed impact (on epics, artifacts, MVP scope), and the rationale for the chosen path forward.
  - **Specific Proposed Edits:** For each affected artifact, clearly show or describe the exact changes (e.g., "Change Story X.Y from: [old text] To: [new text]", "Add new Acceptance Criterion to Story A.B: [new AC]", "Update Section 3.2 of Architecture Document as follows: [new/modified text or diagram description]").
- Present the complete draft of the "Sprint Change Proposal" to the user for final review and feedback. Incorporate any final adjustments requested by the user.

### 5. Finalize & Determine Next Steps

- Obtain explicit user approval for the "Sprint Change Proposal," including all the specific edits documented within it.
- Provide the finalized "Sprint Change Proposal" document to the user.
- **Based on the nature of the approved changes:**
  - **If the approved edits sufficiently address the change and can be implemented directly or organized by a PO/SM:** State that the "Correct Course Task" is complete regarding analysis and change proposal, and the user can now proceed with implementing or logging these changes (e.g., updating actual project documents, backlog items). Suggest handoff to a PO/SM agent for backlog organization if appropriate.
  - **If the analysis and proposed path (as per checklist Section 4 and potentially Section 6) indicate that the change requires a more fundamental replan (e.g., significant scope change, major architectural rework):** Clearly state this conclusion. Advise the user that the next step involves engaging the primary PM or Architect agents, using the "Sprint Change Proposal" as critical input and context for that deeper replanning effort.

## Output Deliverables

- **Primary:** A "Sprint Change Proposal" document (in markdown format). This document will contain:
  - A summary of the `change-checklist` analysis (issue, impact, rationale for the chosen path).
  - Specific, clearly drafted proposed edits for all affected project artifacts.
- **Implicit:** An annotated `change-checklist` (or the record of its completion) reflecting the discussions, findings, and decisions made during the process.

==================== END: correct-course ====================


==================== START: create-ai-frontend-prompt ====================
# Create AI Frontend Prompt Task

## Purpose

To generate a masterful, comprehensive, and optimized prompt that can be used with AI-driven frontend development tools (e.g., Lovable, Vercel v0, or similar) to scaffold or generate significant portions of the frontend application.

## Inputs

- Completed UI/UX Specification (`front-end-spec-tmpl`)
- Completed Frontend Architecture Document (`front-end-architecture`)
- Main System Architecture Document (`architecture` - for API contracts and tech stack)
- Primary Design Files (Figma, Sketch, etc. - for visual context if the tool can accept it or if descriptions are needed)

## Key Activities & Instructions

1.  **Confirm Target AI Generation Platform:**

    - Ask the user to specify which AI frontend generation tool/platform they intend to use (e.g., "Lovable.ai", "Vercel v0", "GPT-4 with direct code generation instructions", etc.).
    - Explain that prompt optimization might differ slightly based on the platform's capabilities and preferred input format.

2.  **Synthesize Inputs into a Structured Prompt:**

    - **Overall Project Context:**
      - Briefly state the project's purpose (from brief/PRD).
      - Specify the chosen frontend framework, core libraries, and UI component library (from `front-end-architecture` and main `architecture`).
      - Mention the styling approach (e.g., Tailwind CSS, CSS Modules).
    - **Design System & Visuals:**
      - Reference the primary design files (e.g., Figma link).
      - If the tool doesn't directly ingest design files, describe the overall visual style, color palette, typography, and key branding elements (from `front-end-spec-tmpl`).
      - List any global UI components or design tokens that should be defined or adhered to.
    - **Application Structure & Routing:**
      - Describe the main pages/views and their routes (from `front-end-architecture` - Routing Strategy).
      - Outline the navigation structure (from `front-end-spec-tmpl`).
    - **Key User Flows & Page-Level Interactions:**
      - For a few critical user flows (from `front-end-spec-tmpl`):
        - Describe the sequence of user actions and expected UI changes on each relevant page.
        - Specify API calls to be made (referencing API endpoints from the main `architecture`) and how data should be displayed or used.
    - **Component Generation Instructions (Iterative or Key Components):**
      - Based on the chosen AI tool's capabilities, decide on a strategy:
        - **Option 1 (Scaffolding):** Prompt for the generation of main page structures, layouts, and placeholders for components.
        - **Option 2 (Key Component Generation):** Select a few critical or complex components from the `front-end-architecture` (Component Breakdown) and provide detailed specifications for them (props, state, basic behavior, key UI elements).
        - **Option 3 (Holistic, if tool supports):** Attempt to describe the entire application structure and key components more broadly.
      - <important_note>Advise the user that generating an entire complex application perfectly in one go is rare. Iterative prompting or focusing on sections/key components is often more effective.</important_note>
    - **State Management (High-Level Pointers):**
      - Mention the chosen state management solution (e.g., "Use Redux Toolkit").
      - For key pieces of data, indicate if they should be managed in global state.
    - **API Integration Points:**
      - For pages/components that fetch or submit data, clearly state the relevant API endpoints (from `architecture`) and the expected data shapes (can reference schemas in `data-models` or `api-reference` sections of the architecture doc).
    - **Critical "Don'ts" or Constraints:**
      - e.g., "Do not use deprecated libraries." "Ensure all forms have basic client-side validation."
    - **Platform-Specific Optimizations:**
      - If the chosen AI tool has known best practices for prompting (e.g., specific keywords, structure, level of detail), incorporate them. (This might require the agent to have some general knowledge or to ask the user if they know any such specific prompt modifiers for their chosen tool).

3.  **Present and Refine the Master Prompt:**
    - Output the generated prompt in a clear, copy-pasteable format (e.g., a large code block).
    - Explain the structure of the prompt and why certain information was included.
    - Work with the user to refine the prompt based on their knowledge of the target AI tool and any specific nuances they want to emphasize.
    - <important_note>Remind the user that the generated code from the AI tool will likely require review, testing, and further refinement by developers.</important_note>

==================== END: create-ai-frontend-prompt ====================


==================== START: create-architecture ====================
# Architecture Creation Task

## Purpose

- To design a complete, robust, and well-documented technical architecture based on the project requirements (PRD, epics, brief), research findings, and user input.
- To make definitive technology choices and articulate the rationale behind them, leveraging the architecture template as a structural guide.
- To produce all necessary technical artifacts, ensuring the architecture is optimized for efficient implementation, particularly by AI developer agents, and validated against the `architect-checklist`.

## Instructions

1.  **Input Analysis & Dialogue Establishment:**

    - Ensure you have all necessary inputs: PRD document (specifically checking for the 'Technical Assumptions' and 'Initial Architect Prompt' sections for the decided repository and service architecture), project brief, any deep research reports, and optionally a `technical-preferences.md`. Request any missing critical documents.
    - Thoroughly review all inputs.
    - Summarize key technical requirements, constraints, NFRs (Non-Functional Requirements), and the decided repository/service architecture derived from the inputs. Present this summary to the user for confirmation and to ensure mutual understanding.
    - Share initial architectural observations, potential challenges, or areas needing clarification based on the inputs.
      **Establish Interaction Mode for Architecture Creation:**
      - Ask the user: "How would you like to proceed with creating the architecture for this project? We can work:
        A. **Incrementally (Default & Recommended):** We'll go through each architectural decision, document section, or design point step-by-step. I'll present drafts, and we'll seek your feedback and confirmation before moving to the next part. This is best for complex decisions and detailed refinement.
        B. **"YOLO" Mode:** I can produce a more comprehensive initial draft of the architecture (or significant portions) for you to review more broadly first. We can then iterate on specific sections based on your feedback. This can be quicker for generating initial ideas but is generally not recommended if detailed collaboration at each step is preferred."
      - Request the user to select their preferred mode (e.g., "Please let me know if you'd prefer A or B.").
      - Once the user chooses, confirm the selected mode (e.g., "Okay, we will proceed in Incremental mode."). This chosen mode will govern how subsequent steps in this task are executed.

2.  **Resolve Ambiguities & Gather Missing Information:**

    - If key information is missing or requirements are unclear after initial review, formulate specific, targeted questions.
    - **External API Details:** If the project involves integration with external APIs, especially those that are less common or where you lack high confidence in your training data regarding their specific request/response schemas, and if a "Deep Research" phase was not conducted for these APIs:
      - Proactively ask the user to provide precise details. This includes:
        - Links to the official API documentation.
        - Example request structures (e.g., cURL commands, JSON payloads).
        - Example response structures (e.g., JSON responses for typical scenarios, including error responses).
      - Explain that this information is crucial for accurately defining API interaction contracts within the architecture, for creating robust facades/adapters, and for enabling accurate technical planning (e.g., for technical stories or epic refinements).
    - Present questions to the user (batched logically if multiple) and await their input.
    - Document all decisions and clarifications received before proceeding.

3.  **Iterative Technology Selection & Design (Interactive, if not YOLO mode):**

    - For each major architectural component or decision point (e.g., frontend framework, backend language/framework, database system, cloud provider, key services, communication patterns):
      - If multiple viable options exist based on requirements or research, present 2-3 choices, briefly outlining their pros, cons, and relevance to the project. Consider any preferences stated in `technical-preferences.md` when formulating these options and your recommendation.
      - State your recommended choice, providing a clear rationale based on requirements, research findings, user preferences (if known), and best practices (e.g., scalability, cost, team familiarity, ecosystem).
      - Ask for user feedback, address concerns, and seek explicit approval before finalizing the decision.
      - Document the confirmed choice and its rationale within the architecture document.
    - **Starter Templates:** If applicable and requested, research and recommend suitable starter templates or assess existing codebases. Explain alignment with project goals and seek user confirmation.

4.  **Create Technical Artifacts (Incrementally, unless YOLO mode, guided by `architecture-tmpl`):**

    - For each artifact or section of the main Architecture Document:

      - **Explain Purpose:** Briefly describe the artifact/section's importance and what it will cover.
      - **Draft Section-by-Section:** Present a draft of one logical section at a time.
        - Ensure the 'High-Level Overview' and 'Component View' sections accurately reflect and detail the repository/service architecture decided in the PRD.
        - Ensure that documented Coding Standards (either as a dedicated section or referenced) and the 'Testing Strategy' section clearly define:
          - The convention for unit test file location (e.g., co-located with source files, or in a separate folder like `tests/` or `__tests__/`).
          - The naming convention for unit test files (e.g., `*.test.js`, `*.spec.ts`, `test_*.py`).
        - When discussing Coding Standards, inform the user that these will serve as firm rules for the AI developer agent. Emphasize that these standards should be kept to the minimum necessary to prevent undesirable or messy code from the agent. Guide the user to understand that overly prescriptive or obvious standards (e.g., "use SOLID principles," which well-trained LLMs should already know) should be avoided, as the user, knowing the specific agents and tools they will employ, can best judge the appropriate level of detail.
        - **Incorporate Feedback:** Discuss the draft with the user, incorporate their feedback, and iterate as needed.
        - [Offer Advanced Self-Refinement & Elicitation Options](#offer-advanced-self-refinement--elicitation-options)
        - **Seek Approval:** Obtain explicit user approval for the section before moving to the next, or for the entire artifact if drafted holistically (in YOLO mode).

5.  **Identify Missing Technical Stories / Refine Epics (Interactive):**

    - Based on the designed architecture, identify any necessary technical stories/tasks that are not yet captured in the PRD or epics (e.g., "Set up CI/CD pipeline for frontend deployment," "Implement authentication module using JWT," "Create base Docker images for backend services," "Configure initial database schema based on data models").
    - Explain the importance of these technical stories for enabling the functional requirements and successful project execution.
    - Collaborate with the user to refine these stories (clear description, acceptance criteria) and suggest adding them to the project backlog or relevant epics.
    - Review existing epics/stories from the PRD and suggest technical considerations or acceptance criteria refinements to ensure they are implementable based on the chosen architecture. For example, specifying API endpoints to be called, data formats, or critical library versions.
    - After collaboration, prepare a concise summary detailing all proposed additions, updates, or modifications to epics and user stories. If no changes are identified, explicitly state this.

6.  **Validate Architecture Against Checklist & Finalize Output:**
    - Once the main architecture document components have been drafted and reviewed with the user, perform a comprehensive review using the `architect-checklist`.
    - Go through each item in the checklist to ensure the architecture document is comprehensive, addresses all key architectural concerns (e.g., security, scalability, maintainability, testability (including confirmation that coding standards and the testing strategy clearly define unit test location and naming conventions), developer experience), and that proposed solutions are robust.
    - For each checklist item, confirm its status (e.g., \[x] Completed, \[ ] N/A, \[!] Needs Attention).
    - If deficiencies, gaps, or areas needing more detail or clarification are identified based on the checklist:
      - Discuss these findings with the user.
      - Collaboratively make necessary updates, additions, or refinements to the architecture document to address these points.
    - After addressing all checklist points and ensuring the architecture document is robust and complete, present a summary of the checklist review to the user. This summary should highlight:
      - Confirmation that all relevant sections/items of the checklist have been satisfied by the architecture.
      - Any items marked N/A, with a brief justification.
      - A brief note on any significant discussions, decisions, or changes made to the architecture document as a result of the checklist review.
    - **Offer Design Architect Prompt (If Applicable):**
      - If the architecture includes UI components, ask the user if they would like to include a dedicated prompt for a "Design Architect" at the end of the main architecture document.
      - Explain that this prompt can capture specific UI considerations, notes from discussions, or decisions that don't fit into the core technical architecture document but are crucial for the Design Architect.
      - The prompt should also state that the Design Architect will subsequently operate in its specialized mode to define the detailed frontend architecture.
      - If the user agrees, collaboratively draft this prompt and append it to the architecture document.
    - Obtain final user approval for the complete architecture documentation generation.
    - **Recommend Next Steps for UI (If Applicable):**
      - After the main architecture document is finalized and approved:
      - If the project involves a user interface (as should be evident from the input PRD and potentially the architecture document itself mentioning UI components or referencing outputs from a Design Architect's UI/UX Specification phase):
        - Strongly recommend to the user that the next critical step for the UI is to engage the **Design Architect** agent.
        - Specifically, advise them to use the Design Architect's **'Frontend Architecture Mode'**.
        - Explain that the Design Architect will use the now-completed main Architecture Document and the detailed UI/UX specifications (e.g., `front-end-spec-tmpl.txt` or enriched PRD) as primary inputs to define the specific frontend architecture, select frontend libraries/frameworks (if not already decided), structure frontend components, and detail interaction patterns.

### Output Deliverables for Architecture Creation Phase

- A comprehensive Architecture Document, structured according to the `architecture-tmpl` (which is all markdown) or an agreed-upon format, including all sections detailed above.
- Clear Mermaid diagrams for architecture overview, data models, etc.
- A list of new or refined technical user stories/tasks ready for backlog integration.
- A summary of any identified changes (additions, updates, modifications) required for existing epics or user stories, or an explicit confirmation if no such changes are needed.
- A completed `architect-checklist` (or a summary of its validation).
- Optionally, if UI components are involved and the user agrees: A prompt for a "Design Architect" appended to the main architecture document, summarizing relevant UI considerations and outlining the Design Architect's next steps.

## Offer Advanced Self-Refinement & Elicitation Options

(This section is called when needed prior to this)

Present the user with the following list of 'Advanced Reflective, Elicitation & Brainstorming Actions'. Explain that these are optional steps to help ensure quality, explore alternatives, and deepen the understanding of the current section before finalizing it and moving on. The user can select an action by number, or choose to skip this and proceed to finalize the section.

"To ensure the quality of the current section: **[Specific Section Name]** and to ensure its robustness, explore alternatives, and consider all angles, I can perform any of the following actions. Please choose a number (8 to finalize and proceed):

**Advanced Reflective, Elicitation & Brainstorming Actions I Can Take:**

{Instruction for AI Agent: Display the title of each numbered item below. If the user asks what a specific option means, provide a brief explanation of the action you will take, drawing from detailed descriptions tailored for the context.}

1.  **Critical Self-Review & User Goal Alignment**
2.  **Generate & Evaluate Alternative Design Solutions**
3.  **User Journey & Interaction Stress Test (Conceptual)**
4.  **Deep Dive into Design Assumptions & Constraints**
5.  **Usability & Accessibility Audit Review & Probing Questions**
6.  **Collaborative Ideation & UI Feature Brainstorming**
7.  **Elicit 'Unforeseen User Needs' & Future Interaction Questions**
8.  **Finalize this Section and Proceed.**

After I perform the selected action, we can discuss the outcome and decide on any further revisions for this section."

REPEAT by Asking the user if they would like to perform another Reflective, Elicitation & Brainstorming Action UNIT the user indicates it is time to proceed ot the next section (or selects #8)

==================== END: create-architecture ====================


==================== START: create-deep-research-prompt ====================
## Deep Research Phase

Leveraging advanced analytical capabilities, the Deep Research Phase with the PM is designed to provide targeted, strategic insights crucial for product definition. Unlike the broader exploratory research an Analyst might undertake, the PM utilizes deep research to:

- **Validate Product Hypotheses:** Rigorously test assumptions about market need, user problems, and the viability of specific product concepts.
- **Refine Target Audience & Value Proposition:** Gain a nuanced understanding of specific user segments, their precise pain points, and how the proposed product delivers unique value to them.
- **Focused Competitive Analysis:** Analyze competitors through the lens of a specific product idea to identify differentiation opportunities, feature gaps to exploit, and potential market positioning challenges.
- **De-risk PRD Commitments:** Ensure that the problem, proposed solution, and core features are well-understood and validated _before_ detailed planning and resource allocation in the PRD Generation Mode.

Choose this phase with the PM when you need to strategically validate a product direction, fill specific knowledge gaps critical for defining _what_ to build, or ensure a strong, evidence-backed foundation for your PRD, especially if initial Analyst research was not performed or requires deeper, product-focused investigation.

### Purpose

- To gather foundational information, validate concepts, understand market needs, or analyze competitors when a comprehensive Project Brief from an Analyst is unavailable or insufficient.
- To ensure the PM has a solid, data-informed basis for defining a valuable and viable product before committing to PRD specifics.
- To de-risk product decisions by grounding them in targeted research, especially if the user is engaging the PM directly without prior Analyst work or if the initial brief lacks necessary depth.

### Instructions

<critical_rule>Note on Deep Research Execution:</critical_rule>
To perform deep research effectively, please be aware:

- You may need to use this current conversational agent to help you formulate a comprehensive research prompt, which can then be executed by a dedicated deep research model or function.
- Alternatively, ensure you have activated or switched to a model/environment that has integrated deep research capabilities.
  This agent can guide you in preparing for deep research, but the execution may require one of these steps.

1.  **Assess Inputs & Identify Gaps:**
    - Review any existing inputs (user's initial idea, high-level requirements, partial brief from Analyst, etc.).
    - Clearly identify critical knowledge gaps concerning:
      - Target audience (needs, pain points, behaviors, key segments).
      - Market landscape (size, trends, opportunities, potential saturation).
      - Competitive analysis (key direct/indirect competitors, their offerings, strengths, weaknesses, market positioning, potential differentiators for this product).
      - Problem/Solution validation (evidence supporting the proposed solution's value and fit for the identified problem).
      - High-level technical or resource considerations (potential major roadblocks or dependencies).
2.  **Formulate Research Plan:**
    - Define specific, actionable research questions to address the identified gaps.
    - Propose targeted research activities (e.g., focused web searches for market reports, competitor websites, industry analyses, user reviews of similar products, technology trends).
    - <important_note>Confirm this research plan, scope, and key questions with the user before proceeding with research execution.</important_note>
3.  **Execute Research:**
    - Conduct the planned research activities systematically.
    - Prioritize gathering credible, relevant, and actionable insights that directly inform product definition and strategy.
4.  **Synthesize & Present Findings:**
    - Organize and summarize key research findings in a clear, concise, and easily digestible manner (e.g., bullet points, brief summaries per research question).
    - Highlight the most critical implications for the product's vision, strategy, target audience, core features, and potential risks.
    - Present these synthesized findings and their implications to the user.
5.  **Discussing and Utilizing Research Output:**
    - The comprehensive findings/report from this Deep Research phase can be substantial. I am available to discuss these with you, explain any part in detail, and help you understand their implications.
    - **Options for Utilizing These Findings for PRD Generation:**
      1.  **Full Handoff to New PM Session:** The complete research output can serve as a foundational document if you initiate a _new_ session with a Product Manager (PM) agent who will then execute the 'PRD Generate Task'.
      2.  **Key Insights Summary for This Session:** I can prepare a concise summary of the most critical findings, tailored to be directly actionable as we (in this current session) transition to potentially invoking the 'PRD Generate Task'.
    - <critical_rule>Regardless of how you proceed, it is highly recommended that these research findings (either the full output or the key insights summary) are provided as direct input when invoking the 'PRD Generate Task'. This ensures the PRD is built upon a solid, evidence-based foundation.</critical_rule>
6.  **Confirm Readiness for PRD Generation:**
    - Discuss with the user whether the gathered information provides a sufficient and confident foundation to proceed to the 'PRD Generate Task'.
    - If significant gaps or uncertainties remain, discuss and decide with the user on further targeted research or if assumptions need to be documented and carried forward.
    - Once confirmed, clearly state that the next step could be to invoke the 'PRD Generate Task' or, if applicable, revisit other phase options.

==================== END: create-deep-research-prompt ====================


==================== START: create-frontend-architecture ====================
# Create Frontend Architecture Task

## Purpose

To define the technical architecture for the frontend application. This includes selecting appropriate patterns, structuring the codebase, defining component strategy, planning state management, outlining API interactions, and setting up testing and deployment approaches, all while adhering to the guidelines in `front-end-architecture-tmpl` template.

## Inputs

- Product Requirements Document (PRD) (`prd-tmpl` or equivalent)
- Completed UI/UX Specification (`front-end-spec-tmpl` or equivalent)
- Main System Architecture Document (`architecture` or equivalent) - The agent executing this task should particularly note the overall system structure (Monorepo/Polyrepo, backend service architecture) detailed here, as it influences frontend patterns.
- Primary Design Files (Figma, Sketch, etc., linked from UI/UX Spec)

## Key Activities & Instructions

### 1. Confirm Interaction Mode

- Ask the user: "How would you like to proceed with creating the frontend architecture? We can work:
  A. **Incrementally (Default & Recommended):** We'll go through each architectural decision and document section step-by-step. I'll present drafts, and we'll seek your feedback and confirmation before moving to the next part. This is best for complex decisions and detailed refinement.
  B. **"YOLO" Mode:** I can produce a more comprehensive initial draft of the frontend architecture for you to review more broadly first. We can then iterate on specific sections based on your feedback. This can be quicker for generating initial ideas but is generally not recommended if detailed collaboration at each step is preferred."
- Request the user to select their preferred mode (e.g., "Please let me know if you'd prefer A or B.").
- Once the user chooses, confirm the selected mode (e.g., "Okay, we will proceed in Incremental mode."). This chosen mode will govern how subsequent steps are executed.

### 2. Review Inputs & Establish Context

- Thoroughly review the inputs, including the UI/UX Specification and the main Architecture Document (especially "Definitive Tech Stack Selections", API contracts, and the documented overall system structure like monorepo/polyrepo choices).
- Ask clarifying questions to bridge any gaps between the UI/UX vision and the overall system architecture.

### 3. Define Overall Frontend Philosophy & Patterns (for `front-end-architecture`)

- Based on the main architecture's tech stack and overall system structure (monorepo/polyrepo, backend service details), confirm and detail:
  - Framework & Core Libraries choices.
  - High-level Component Architecture strategy.
  - High-level State Management Strategy.
  - Data Flow principles.
  - Styling Approach.
  - Key Design Patterns to be employed.

### 4. Specify Detailed Frontend Directory Structure (for `front-end-architecture`)

- Collaboratively define or refine the frontend-specific directory structure, ensuring it aligns with the chosen framework and promotes modularity and scalability.

### 5. Outline Component Strategy & Conventions (for `front-end-architecture`)

- Define Component Naming & Organization conventions.
- Establish the "Template for Component Specification" (as per `front-end-architecture`), emphasizing that most components will be detailed emergently but must follow this template.
- Optionally, specify a few absolutely foundational/shared UI components (e.g., a generic Button or Modal wrapper if the chosen UI library needs one, or if no UI library is used).

### 6. Detail State Management Setup & Conventions (for `front-end-architecture`)

- Based on the high-level strategy, detail:
  - Chosen Solution and core setup.
  - Conventions for Store Structure / Slices (e.g., "feature-based slices"). Define any genuinely global/core slices (e.g., session/auth).
  - Conventions for Selectors and Actions/Reducers/Thunks. Provide templates or examples.

### 7. Plan API Interaction Layer (for `front-end-architecture`)

- Define the HTTP Client Setup.
- Establish patterns for Service Definitions (how API calls will be encapsulated).
- Outline frontend Error Handling & Retry strategies for API calls.

### 8. Define Routing Strategy (for `front-end-architecture`)

- Confirm the Routing Library.
- Collaboratively define the main Route Definitions and any Route Guards.

### 9. Specify Build, Bundling, and Deployment Details (for `front-end-architecture`)

- Outline the frontend-specific Build Process & Scripts.
- Discuss and document Key Bundling Optimizations.
- Confirm Deployment to CDN/Hosting details relevant to the frontend.

### 10. Refine Frontend Testing Strategy (for `front-end-architecture`)

- Elaborate on the main testing strategy with specifics for: Component Testing, UI Integration/Flow Testing, and E2E UI Testing scope and tools.

### 11. Outline Performance Considerations (for `front-end-architecture`)

- List key frontend-specific performance strategies to be employed.

### 12. Document Drafting & Confirmation (Guided by `front-end-architecture-tmpl`)

- **If "Incremental Mode" was selected:**

  - For each relevant section of the `front-end-architecture` (as outlined in steps 3-11 above, covering topics from Overall Philosophy to Performance Considerations):

    - **a. Explain Purpose & Draft Section:** Explain the purpose of the section and present a draft for that section.
    - **b. Initial Discussion & Feedback:** Discuss the draft with the user, incorporate their feedback, and iterate as needed for initial revisions.
    - **c. [Offer Advanced Self-Refinement & Elicitation Options](#offer-advanced-self-refinement--elicitation-options)**

    - **d. Final Approval & Documentation:** Obtain explicit user approval for the section. Ensure all placeholder links and references are correctly noted within each section. Then proceed to the next section.

  - Once all sections are individually approved through this process, confirm with the user that the overall `front-end-architecture` document is populated and ready for Step 13 (Epic/Story Impacts) and then the checklist review (Step 14).

- **If "YOLO Mode" was selected:**
  - Collaboratively populate all relevant sections of the `front-end-architecture-tmpl` (as outlined in steps 3-11 above) to create a comprehensive first draft.
  - Present the complete draft of `front-end-architecture` to the user for a holistic review.
  - <important_note>After presenting the full draft in YOLO mode, you MAY still offer a condensed version of the 'Advanced Reflective & Elicitation Options' menu, perhaps focused on a few key overarching review actions (e.g., overall requirements alignment, major risk assessment) if the user wishes to perform a structured deep dive before detailed section-by-section feedback.</important_note>
  - Obtain explicit user approval for the entire `front-end-architecture` document before proceeding to Step 13 (Epic/Story Impacts) and then the checklist review (Step 14).

### 13. Identify & Summarize Epic/Story Impacts (Frontend Focus)

- After the `front-end-architecture` is confirmed, review it in context of existing epics and user stories (if provided or known).
- Identify any frontend-specific technical tasks that might need to be added as new stories or sub-tasks (e.g., "Implement responsive layout for product details page based on defined breakpoints," "Set up X state management slice for user profile," "Develop reusable Y component as per specification").
- Identify if any existing user stories require refinement of their acceptance criteria due to frontend architectural decisions (e.g., specifying interaction details, component usage, or performance considerations for UI elements).
- Collaborate with the user to define these additions or refinements.
- Prepare a concise summary detailing all proposed additions, updates, or modifications to epics and user stories related to the frontend. If no changes are identified, explicitly state this (e.g., "No direct impacts on existing epics/stories were identified from the frontend architecture").

### 14. Checklist Review and Finalization

- Once the `front-end-architecture` has been populated and reviewed with the user, and epic/story impacts have been summarized, use the `frontend-architecture-checklist`.
- Go through each item in the checklist to ensure the `front-end-architecture` is comprehensive and all sections are adequately addressed - for each checklist item you MUST consider if it is really complete or deficient.
- For each checklist section, confirm its status (e.g., \[x] Completed, \[ ] N/A, \[!] Needs Attention).
- If deficiencies or areas needing more detail are identified with a section:
  - Discuss these with the user.
  - Collaboratively make necessary updates or additions to the `front-end-architecture`.
- After addressing all points and ensuring the document is robust, present a summary of the checklist review to the user. This summary should highlight:
  - Confirmation that all relevant sections of the checklist have been satisfied.
  - Any items marked N/A and a brief reason.
  - A brief note on any significant discussions or changes made as a result of the checklist review.
- The goal is to ensure the `front-end-architecture` is a complete and actionable document.

## Offer Advanced Self-Refinement & Elicitation Options

(This section is called when needed prior to this)

Present the user with the following list of 'Advanced Reflective, Elicitation & Brainstorming Actions'. Explain that these are optional steps to help ensure quality, explore alternatives, and deepen the understanding of the current section before finalizing it and moving on. The user can select an action by number, or choose to skip this and proceed to finalize the section.

"To ensure the quality of the current section: **[Specific Section Name]** and to ensure its robustness, explore alternatives, and consider all angles, I can perform any of the following actions. Please choose a number (8 to finalize and proceed):

**Advanced Reflective, Elicitation & Brainstorming Actions I Can Take:**

{Instruction for AI Agent: Display the title of each numbered item below. If the user asks what a specific option means, provide a brief explanation of the action you will take, drawing from detailed descriptions tailored for the context.}

1.  **Critical Self-Review & User Goal Alignment**
2.  **Generate & Evaluate Alternative Design Solutions**
3.  **User Journey & Interaction Stress Test (Conceptual)**
4.  **Deep Dive into Design Assumptions & Constraints**
5.  **Usability & Accessibility Audit Review & Probing Questions**
6.  **Collaborative Ideation & UI Feature Brainstorming**
7.  **Elicit 'Unforeseen User Needs' & Future Interaction Questions**
8.  **Finalize this Section and Proceed.**

After I perform the selected action, we can discuss the outcome and decide on any further revisions for this section."

REPEAT by Asking the user if they would like to perform another Reflective, Elicitation & Brainstorming Action UNIT the user indicates it is time to proceed ot the next section (or selects #8)

==================== END: create-frontend-architecture ====================


==================== START: create-master-prd ====================
# Master PRD Generation Task
## System-Level Product Requirements for Microservices Architecture

## Purpose

- Transform Master Project Brief (created by Analyst) into comprehensive system-level product definition documents
- Define clear system-wide scope and cross-cutting concerns for microservices architecture
- Provide foundation for Platform Architect, AI Orchestration Agent, and Individual Service PRDs
- Coordinate dependencies and integration patterns across the entire system

Remember as you follow the upcoming instructions:

- Your documents form the foundation for the entire distributed system development process
- Output will be used by Platform Architect for infrastructure design and AI Orchestration Agent for agent workflow design
- Your system-level requirements will guide individual service PRD creation
- Focus on system-wide "what" while leaving service-specific "how" for individual service PRDs
- You are transforming strategic briefs from Analyst into actionable product requirements

## Instructions

### 1. Define Project Workflow Context

Before Master PRD generation, ask the user to choose their intended workflow approach:

A. **System-First Approach (Recommended):** Create comprehensive system-level PRD first, then generate individual service PRDs based on system requirements and service catalog.

B. **Parallel Development Approach:** Create system-level PRD while simultaneously planning individual service PRDs for critical services.

C. **Iterative Approach:** Create initial system-level PRD, validate with key service PRDs, then iterate and refine both levels.

### 2. Determine Interaction Mode

Confirm with the user their preferred interaction style:
- **Incremental (Default):** Address Master PRD sections sequentially, seeking feedback on each
- **"YOLO" Mode:** Draft comprehensive Master PRD for single, larger review

### 3. Review System-Level Inputs

Review all inputs provided:
- Master Project Brief (created by Analyst)
- Service discovery analysis (if available)
- AI integration strategy (if available)
- Platform requirements analysis (if available)
- Any research or stakeholder input

### 4. Process Master PRD Sections

Work through the Master PRD sections using the `master-project-prd-tmpl` template:

#### 4A. System Context and Architecture
- **Project Context and Objectives**: System-wide business goals and strategic objectives
- **System Architecture Overview**: High-level topology and major components
- **Service Catalog Definition**: Complete inventory of planned microservices
- **Communication Patterns**: Inter-service protocols and integration strategies

#### 4B. Cross-Cutting Requirements
- **Security and Compliance Framework**: System-wide security and regulatory requirements
- **Performance and Scalability**: System-level SLAs and scaling strategies
- **Reliability and Resilience**: Fault tolerance and business continuity requirements
- **Observability and Monitoring**: Centralized logging, metrics, and alerting

#### 4C. AI Agent Ecosystem Design
- **Agent Orchestration Service**: Multi-agent workflow coordination requirements
- **Intelligence Hub Service**: Centralized analytics and predictive capabilities
- **Conversational AI Service**: Natural language processing requirements
- **Automation Engine Service**: Task automation and decision execution
- **Learning & Adaptation Service**: Continuous improvement and model evolution

#### 4D. Platform Engineering Strategy
- **Internal Developer Platform Requirements**: Self-service capabilities and golden paths
- **Infrastructure Requirements**: Kubernetes, service mesh, and cloud services
- **Developer Experience Optimization**: Tooling, automation, and productivity
- **Platform Team Responsibilities**: Platform-as-a-product approach

### 5. Epic and Service Breakdown Strategy

#### 5A. System-Level Epic Definition
Create epics that span multiple services and deliver end-to-end value:
- **Epic 1: Platform Foundation** - Infrastructure, security, core services
- **Epic 2: Core Business Services** - Primary business logic and workflows
- **Epic 3: Data Services** - Analytics, intelligence, data management
- **Epic 4: Integration Services** - External APIs and legacy connectivity
- **Epic 5: AI Agent Services** - Agentic AI capabilities and orchestration
- **Epic 6: Frontend Applications** - User interfaces and micro-frontend architecture

#### 5B. Service Dependency Matrix
Create comprehensive mapping of:
- Cross-service relationships and communication patterns
- API contract specifications and integration requirements
- Event-driven architecture and message schemas
- External integrations and third-party dependencies

### 6. AI Integration and Human-AI Collaboration

#### 6A. Multi-Agent Workflow Design
- **Agent Coordination Patterns**: How AI agents collaborate across services
- **Task Distribution Logic**: Intelligent routing based on agent capabilities
- **Human-AI Handoff Procedures**: Escalation protocols and collaboration patterns
- **Context Preservation**: Memory and state management across agent interactions

#### 6B. AI Infrastructure Planning
- **Vector Database Strategy**: Embedding storage and semantic search requirements
- **Model Serving Architecture**: Inference scaling and model management
- **AI Observability**: Monitoring, evaluation, and performance tracking
- **AI Governance Framework**: Ethics, compliance, and quality assurance

### 7. Platform and Infrastructure Requirements

#### 7A. Internal Developer Platform Design
- **Self-Service Capabilities**: Golden paths and developer portal requirements
- **Platform Service Catalog**: Available services and their interfaces
- **Developer Experience**: Tooling, automation, and productivity optimization
- **Platform Team Topology**: Team structure and responsibility definition

#### 7B. Infrastructure Architecture
- **Kubernetes Strategy**: Cluster design and resource management
- **Service Mesh Planning**: Istio/Linkerd implementation requirements
- **Networking and Security**: Zero Trust architecture and communication patterns
- **Monitoring and Observability**: Centralized logging, metrics, and tracing

### 8. Implementation Timeline and Coordination

#### 8A. Phased Delivery Strategy
- **Phase 1: Foundation** - Platform setup and core infrastructure
- **Phase 2: Core Services** - Business logic and data services
- **Phase 3: AI Integration** - Agentic AI capabilities and orchestration
- **Phase 4: Advanced Features** - Advanced capabilities and optimization

#### 8B. Cross-Service Coordination
- **Dependency Management**: Service interdependencies and coordination requirements
- **Integration Testing**: Contract testing and end-to-end validation
- **Deployment Orchestration**: Coordinated deployment across services
- **Change Management**: System-wide change coordination and communication

### 9. Quality Assurance and Governance

#### 9A. System-Level Quality Gates
- **Architecture Compliance**: Validation against architectural principles
- **Security Validation**: System-wide security and compliance verification
- **Performance Testing**: End-to-end performance and scalability validation
- **Integration Testing**: Cross-service interaction and contract validation

#### 9B. Governance Framework
- **Change Management**: System-wide change approval and coordination
- **Documentation Standards**: Consistent documentation across all services
- **Quality Standards**: Code quality, testing, and operational excellence
- **Compliance Management**: Regulatory requirements and audit procedures

### 10. Handoff Instructions and Next Steps

#### 10A. Platform Architect Handoff
Create detailed prompt for Platform Architect including:
- Infrastructure requirements and constraints
- IDP capabilities and developer experience needs
- Kubernetes and service mesh requirements
- Monitoring and operational excellence requirements

#### 10B. AI Orchestration Agent Handoff
Create detailed prompt for AI Orchestration Agent including:
- Multi-agent workflow requirements
- Human-AI collaboration patterns
- AI infrastructure and scaling needs
- AI governance and quality requirements

#### 10C. Individual Service PRD Generation
Create framework for generating individual service PRDs:
- Service catalog with detailed specifications
- Cross-service dependency requirements
- Integration patterns and communication protocols
- Quality and compliance requirements for each service

### 11. Validation and Iteration

#### 11A. System Coherence Validation
- **Architecture Consistency**: Ensure all components work together coherently
- **Dependency Validation**: Verify all dependencies are properly addressed
- **Integration Completeness**: Ensure all integration points are covered
- **Quality Completeness**: Verify all quality requirements are addressed

#### 11B. Stakeholder Review and Approval
- **Technical Review**: Architecture and technical feasibility validation
- **Business Review**: Business value and strategic alignment validation
- **Operational Review**: Operational feasibility and resource requirements
- **Compliance Review**: Regulatory and governance requirements validation

## Deliverables

### Primary Deliverable
Complete Master Project PRD following the `master-project-prd-tmpl` template with:
- Comprehensive system-level requirements
- Service catalog and dependency matrix
- AI agent ecosystem specifications
- Platform engineering requirements
- Implementation timeline and coordination plan

### Secondary Deliverables
- Platform Architect prompt with infrastructure requirements
- AI Orchestration Agent prompt with workflow requirements
- Individual Service PRD generation framework
- System-level quality assurance and governance framework

## Success Criteria

- Master PRD provides clear system-wide vision and requirements
- All cross-cutting concerns are properly addressed
- Service boundaries and dependencies are clearly defined
- AI integration strategy is comprehensive and actionable
- Platform requirements enable autonomous team development
- Implementation plan is realistic and achievable
- Quality and governance frameworks ensure system coherence

==================== END: create-master-prd ====================


==================== START: create-next-story-task ====================
# Create Next Story Task

## Purpose

To identify the next logical story based on project progress and epic definitions, and then to prepare a comprehensive, self-contained, and actionable story file using the `Story Template`. This task ensures the story is enriched with all necessary technical context, requirements, and acceptance criteria, making it ready for efficient implementation by a Developer Agent with minimal need for additional research.

## Inputs for this Task

- Access to the project's documentation repository, specifically:
  - `docs/index.md` (hereafter "Index Doc")
  - All Epic files (e.g., `docs/epic-{n}.md` - hereafter "Epic Files")
  - Existing story files in `docs/stories/`
  - Main PRD (hereafter "PRD Doc")
  - Main Architecture Document (hereafter "Main Arch Doc")
  - Frontend Architecture Document (hereafter "Frontend Arch Doc," if relevant)
  - Project Structure Guide (`docs/project-structure.md`)
  - Operational Guidelines Document (`docs/operational-guidelines.md`)
  - Technology Stack Document (`docs/tech-stack.md`)
  - Data Models Document (as referenced in Index Doc)
  - API Reference Document (as referenced in Index Doc)
  - UI/UX Specifications, Style Guides, Component Guides (if relevant, as referenced in Index Doc)
- The `bmad-agent/templates/story-tmpl.md` (hereafter "Story Template")
- The `bmad-agent/checklists/story-draft-checklist.md` (hereafter "Story Draft Checklist")
- User confirmation to proceed with story identification and, if needed, to override warnings about incomplete prerequisite stories.

## Task Execution Instructions

### 1. Identify Next Story for Preparation

- Review `docs/stories/` to find the highest-numbered story file.
- **If a highest story file exists (`{lastEpicNum}.{lastStoryNum}.story.md`):**

  - Verify its `Status` is 'Done' (or equivalent).
  - If not 'Done', present an alert to the user:

    ```
    ALERT: Found incomplete story:
    File: {lastEpicNum}.{lastStoryNum}.story.md
    Status: [current status]

    Would you like to:
    1. View the incomplete story details (instructs user to do so, agent does not display)
    2. Cancel new story creation at this time
    3. Accept risk & Override to create the next story in draft

    Please choose an option (1/2/3):
    ```

  - Proceed only if user selects option 3 (Override) or if the last story was 'Done'.
  - If proceeding: Check the Epic File for `{lastEpicNum}` for a story numbered `{lastStoryNum + 1}`. If it exists and its prerequisites (per Epic File) are met, this is the next story.
  - Else (story not found or prerequisites not met): The next story is the first story in the next Epic File (e.g., `docs/epic-{lastEpicNum + 1}.md`, then `{lastEpicNum + 2}.md`, etc.) whose prerequisites are met.

- **If no story files exist in `docs/stories/`:**
  - The next story is the first story in `docs/epic-1.md` (then `docs/epic-2.md`, etc.) whose prerequisites are met.
- If no suitable story with met prerequisites is found, report to the user that story creation is blocked, specifying what prerequisites are pending. HALT task.
- Announce the identified story to the user: "Identified next story for preparation: {epicNum}.{storyNum} - {Story Title}".

### 2. Gather Core Story Requirements (from Epic File)

- For the identified story, open its parent Epic File.
- Extract: Exact Title, full Goal/User Story statement, initial list of Requirements, all Acceptance Criteria (ACs), and any predefined high-level Tasks.
- Keep a record of this original epic-defined scope for later deviation analysis.

### 3. Gather & Synthesize In-Depth Technical Context for Dev Agent

- <critical_rule>Systematically use the Index Doc (`docs/index.md`) as your primary guide to discover paths to ALL detailed documentation relevant to the current story's implementation needs.</critical_rule>
- Thoroughly review the PRD Doc, Main Arch Doc, and Frontend Arch Doc (if a UI story).
- Guided by the Index Doc and the story's needs, locate, analyze, and synthesize specific, relevant information from sources such as:
  - Data Models Doc (structure, validation rules).
  - API Reference Doc (endpoints, request/response schemas, auth).
  - Applicable architectural patterns or component designs from Arch Docs.
  - UI/UX Specs, Style Guides, Component Guides (for UI stories).
  - Specifics from Tech Stack Doc if versions or configurations are key for this story.
  - Relevant sections of the Operational Guidelines Doc (e.g., story-specific error handling nuances, security considerations for data handled in this story).
- The goal is to collect all necessary details the Dev Agent would need, to avoid them having to search extensively. Note any discrepancies between the epic and these details for "Deviation Analysis."

### 4. Verify Project Structure Alignment

- Cross-reference the story's requirements and anticipated file manipulations with the Project Structure Guide (and frontend structure if applicable).
- Ensure any file paths, component locations, or module names implied by the story align with defined structures.
- Document any structural conflicts, necessary clarifications, or undefined components/paths in a "Project Structure Notes" section within the story draft.

### 5. Populate Story Template with Full Context

- Create a new story file: `docs/stories/{epicNum}.{storyNum}.story.md`.
- Use the Story Template to structure the file.
- Fill in:
  - Story `{EpicNum}.{StoryNum}: {Short Title Copied from Epic File}`
  - `Status: Draft`
  - `Story` (User Story statement from Epic)
  - `Acceptance Criteria (ACs)` (from Epic, to be refined if needed based on context)
- **`Dev Technical Guidance` section (CRITICAL):**
  - Based on all context gathered (Step 3 & 4), embed concise but critical snippets of information, specific data structures, API endpoint details, precise references to _specific sections_ in other documents (e.g., "See `Data Models Doc#User-Schema-ValidationRules` for details"), or brief explanations of how architectural patterns apply to _this story_.
  - If UI story, provide specific references to Component/Style Guides relevant to _this story's elements_.
  - The aim is to make this section the Dev Agent's primary source for _story-specific_ technical context.
- **`Tasks / Subtasks` section:**
  - Generate a detailed, sequential list of technical tasks and subtasks the Dev Agent must perform to complete the story, informed by the gathered context.
  - Link tasks to ACs where applicable (e.g., `Task 1 (AC: 1, 3)`).
- Add notes on project structure alignment or discrepancies found in Step 4.
- Prepare content for the "Deviation Analysis" based on discrepancies noted in Step 3.

==================== END: create-next-story-task ====================


==================== START: create-prd ====================
# Comprehensive PRD Generation Task

## Purpose

- Transform business requirements into comprehensive product definition documents for diverse architectural patterns
- Create comprehensive PRDs for monolithic and modular application development
- Support enterprise-grade flexibility for various development paradigms
- Enable intelligent system design with AI-enhanced development processes

Remember as you follow the upcoming instructions:

- This task is for comprehensive PRDs supporting diverse architectural patterns
- For microservices projects, use "Create Master PRD" or "Create Service PRD" tasks instead
- Output will be used for modern application development with enterprise-grade flexibility
- Focus on monolithic and modular application patterns with AI-enhanced workflows
- Consider this for projects requiring comprehensive architecture support

## Instructions

### 1. Define Microservices Project Context

- Before PRD generation, determine the microservices project scope and approach:

  **Core Microservices PRD Types:**
  A. **Master System PRD (Default):** Create comprehensive system-level PRD for entire microservices ecosystem with service catalog, cross-cutting concerns, and platform engineering requirements

  B. **Individual Service PRD:** Create detailed PRD for a specific microservice within an existing ecosystem (requires existing Master PRD for context)

  C. **AI Agent Integration PRD:** Focus on agentic AI capabilities, multi-agent orchestration, and human-AI collaboration patterns

  D. **Platform Engineering PRD:** Define Internal Developer Platform requirements, developer experience optimization, and platform-as-a-product approach

  **Advanced Microservices Specifications:**
  E. **Service Integration Contract:** Define cross-service communication contracts, API specifications, and event schemas

  F. **Event-Driven Architecture Specification:** Plan event sourcing, CQRS, and distributed event patterns

- Explain this choice determines the template structure and specialized requirements focus

### 2. Determine Interaction Mode and Template Selection

- Confirm the user's preferred interaction style for creating the PRD:
  - **Incremental Mode (Default):** Address PRD sections sequentially, seeking feedback on each section. For service catalogs and epics: present the ordered list for approval, then detail each component systematically.
  - **"YOLO" Mode:** Draft comprehensive PRD with multiple sections, service specifications, and integration contracts for single, comprehensive review.

### 3. Review Microservices Context and Requirements

Review all available inputs including:
- Master Project Brief (if available)
- Service boundary analysis and domain modeling
- AI integration strategy and agent requirements
- Platform engineering needs and developer experience requirements
- Cross-service dependencies and integration patterns

### 4. Select and Process Microservices Template

Based on the selected PRD type, use the appropriate microservices-native template:

**Template Selection Logic:**
- **Master System PRD:** Use `master-project-prd-tmpl` for comprehensive ecosystem specification
- **Individual Service PRD:** Use `individual-service-prd-tmpl` with Master PRD context alignment
- **AI Agent Integration:** Use `ai-agent-integration-tmpl` for agentic AI specifications
- **Platform Engineering:** Use `platform-engineering-strategy-tmpl` for IDP requirements
- **Service Integration:** Use `service-integration-contract-tmpl` for cross-service contracts
- **Event-Driven Architecture:** Use `event-schema-definition-tmpl` for event specifications

<important_note>For all microservices PRDs, explicitly address distributed system considerations including service boundaries, communication patterns, data consistency models, and platform engineering requirements. Ensure alignment with enterprise architecture principles, Conway's Law implications, and team topology patterns. Document technology stack decisions for microservices ecosystem including container orchestration, service mesh, event streaming, and AI infrastructure.</important_note>

### 5. Process Microservices PRD Sections

Work through the selected template sections systematically, addressing microservices-specific considerations:

**For Master System PRD:**
- **System Vision and Strategy:** Define overall ecosystem purpose and business value
- **Service Catalog and Boundaries:** Identify services using domain-driven design principles
- **Cross-Cutting Concerns:** Address security, monitoring, compliance across all services
- **Platform Engineering Requirements:** Define IDP capabilities and developer experience needs
- **AI Integration Strategy:** Plan agentic AI placement and orchestration across services
- **Technology Strategy:** Establish technology stack for microservices ecosystem

**For Individual Service PRD:**
- **Service Purpose and Boundaries:** Define service responsibility and domain boundaries
- **Service Dependencies:** Identify upstream and downstream service relationships
- **API and Event Contracts:** Specify service interfaces and communication patterns
- **Data Management:** Define service data ownership and consistency requirements
- **Performance and Scaling:** Establish service-specific performance and scaling requirements

**For AI Agent Integration PRD:**
- **Agent Capabilities and Purpose:** Define AI agent functionality and business value
- **Human-AI Collaboration:** Specify handoff procedures and escalation protocols
- **Multi-Agent Orchestration:** Plan agent coordination and workflow patterns
- **Service Integration:** Define how agents integrate with microservices architecture
- **AI Governance and Ethics:** Establish AI governance and compliance frameworks

### 6. Microservices Epic and Story Generation

For microservices PRDs, organize work into service-oriented epics and cross-service coordination stories:

#### 6A. Service-Oriented Epic Strategy

**For Master System PRD:**
- **Platform Foundation Epic:** Establish IDP, CI/CD, monitoring, and security infrastructure
- **Core Service Development Epics:** One epic per major service or bounded context
- **Cross-Service Integration Epics:** Service communication, event-driven workflows, and data consistency
- **AI Integration Epics:** Agent deployment, orchestration, and human-AI collaboration
- **Operational Excellence Epics:** Monitoring, alerting, incident response, and compliance

**For Individual Service PRD:**
- **Service Foundation Epic:** Service infrastructure, deployment, and basic functionality
- **Core Business Logic Epics:** Domain-specific functionality and business rules
- **Integration Epics:** API contracts, event handling, and service dependencies
- **Performance and Scaling Epics:** Optimization, caching, and scaling capabilities

#### 6B. Cross-Service Story Coordination

**Service Dependency Management:**
- Identify upstream and downstream service dependencies
- Define integration contracts and API specifications
- Plan event-driven communication patterns
- Establish data consistency and transaction boundaries

**Platform Engineering Stories:**
- Self-service capabilities and golden paths
- Developer experience optimization
- Monitoring and observability integration
- Security and compliance automation

**AI Integration Stories:**
- Agent deployment and configuration
- Human-AI collaboration workflows
- Multi-agent orchestration patterns
- AI governance and ethics implementation

### 7. Complete Microservices PRD Draft

Present the user with the complete microservices PRD draft once all sections are completed (or as per YOLO mode interaction).

### 8. Microservices Architecture Handoff

Based on the PRD type, recommend appropriate next steps:

**For Master System PRD:**
- **Platform Engineer:** Design Internal Developer Platform and developer experience
- **Service Mesh Architect:** Plan service communication and infrastructure
- **AI Orchestration Specialist:** Design multi-agent systems and AI integration

**For Individual Service PRD:**
- **Service Architect:** Design service-specific architecture and implementation
- **Integration Specialist:** Define service contracts and communication patterns

**For AI Agent Integration PRD:**
- **AI Orchestration Specialist:** Implement agent capabilities and orchestration
- **Platform Engineer:** Integrate AI infrastructure with platform capabilities

### 9. Microservices Checklist Assessment

Use microservices-specific checklists to validate PRD completeness:
- **Service Boundary Validation:** Ensure services align with business capabilities
- **Integration Contract Completeness:** Verify all service communications are documented
- **Platform Engineering Requirements:** Validate IDP and developer experience needs
- **AI Integration Specifications:** Confirm AI agent and orchestration requirements
- **Cross-Cutting Concerns:** Ensure security, monitoring, and compliance are addressed

### 10. Produce the Microservices PRD

Produce the PRD using the selected microservices template with the following guidance:

**General Presentation & Content:**

- Present Project Briefs (drafts or final) in a clean, full format.
- Crucially, DO NOT truncate information that has not changed from a previous version.
- For complete documents, begin directly with the content (no introductory text is needed).

<important_note>
**Next Steps for UI/UX Specification (If Applicable):**

- If the product described in this PRD includes a user interface:

  1.  **Include Design Architect Prompt in PRD:** You will add a dedicated section in the PRD document you are producing, specifically at the location marked `(END Checklist START Design Architect UI/UX Specification Mode Prompt)` (as per the `prd-tmpl` structure). This section will contain a prompt for the **Design Architect** agent.

      - The prompt should clearly state that the Design Architect is to operate in its **'UI/UX Specification Mode'**.

      - It should instruct the Design Architect to use this PRD as primary input to collaboratively define and document detailed UI/UX specifications. This might involve creating/populating a `front-end-spec-tmpl` and ensuring key UI/UX considerations are integrated or referenced back into the PRD to enrich it.

      - Example prompt text to insert:

        ```markdown
        ## Prompt for Design Architect (UI/UX Specification Mode)

        **Objective:** Elaborate on the UI/UX aspects of the product defined in this PRD.
        **Mode:** UI/UX Specification Mode
        **Input:** This completed PRD document.
        **Key Tasks:**

        1. Review the product goals, user stories, and any UI-related notes herein.
        2. Collaboratively define detailed user flows, wire-frames (conceptual), and key screen mockups/descriptions.
        3. Specify usability requirements and accessibility considerations.
        4. Populate or create the `front-end-spec-tmpl` document.
        5. Ensure that this PRD is updated or clearly references the detailed UI/UX specifications derived from your work, so that it provides a comprehensive foundation for subsequent architecture and development phases.

        Please guide the user through this process to enrich the PRD with detailed UI/UX specifications.
        ```

  2.  **Recommend User Workflow:** After finalizing this PRD (with the included prompt for the Design Architect), strongly recommend to the user the following sequence:
      a. First, engage the **Design Architect** agent (using the prompt you've embedded in the PRD) to operate in **'UI/UX Specification Mode'**. Explain that this step is crucial for detailing the user interface and experience, and the output (e.g., a populated `front-end-spec-tmpl` and potentially updated PRD sections) will be vital.
      b. Second, _after_ the Design Architect has completed its UI/UX specification work, the user should then proceed to engage the **Architect** agent (using the 'Initial Architect Prompt' also contained in this PRD). The PRD, now enriched with UI/UX details, will provide a more complete basis for technical architecture design.

- If the product does not include a user interface, you will simply recommend proceeding to the Architect agent using the 'Initial Architect Prompt' in the PRD.
  </important_note>

## Guiding Principles for Epic and User Story Generation

### I. Strategic Foundation: Define Core Value & MVP Scope Rigorously

Understand & Clarify Core Needs: Start by deeply understanding and clarifying the core problem this product solves, the essential needs of the defined User Personas (or system actors), and the key business objectives for the Minimum Viable Product (MVP).
Challenge Scope Relentlessly: Actively challenge all requested features and scope at every stage. For each potential feature or story, rigorously ask, "Does this directly support the core MVP goals and provide significant value to a target User Persona?" Clearly identify and defer non-essential functionalities to a Post-MVP backlog.

### II. Structuring the Work: Value-Driven Epics & Logical Sequencing

Organize into Deployable, Value-Driven Epics: Structure the MVP scope into Epics. Each Epic must be designed to deliver a significant, end-to-end, and fully deployable increment of testable functionality that provides tangible value to the user or business. Epics should represent logical functional blocks or coherent user journeys.

Logical Epic Sequencing & Foundational Work:
Ensure the sequence of Epics follows a logical implementation order, making dependencies between Epics clear and explicitly managed.
The first Epic must always establish the foundational project infrastructure (e.g., initial app setup, Git repository, CI/CD pipeline, core cloud service configurations, basic user authentication shell if needed universally) necessary to support its own deployable functionality and that of subsequent Epics.
Ensure Logical Story Sequencing and Dependency Awareness within Epics:
After initially drafting all User Stories for an Epic, but before detailed review with the user, you (the AI Agent executing this task) must explicitly perform an internal review to establish a logical sequence for these stories.
For each story, identify if it has direct prerequisite stories within the same Epic or from already completed Epics.
Propose a clear story order to the user, explaining the rationale based on these dependencies (e.g., "Story X needs to be done before Story Y because..."). Make significant dependencies visible, perhaps as a note within the story description.

### III. Crafting Effective User Stories: Vertical Slices Focused on Value & Clarity

Define Stories as "Vertical Slices": Within each Epic, define User Stories as "vertical slices". This means each story must deliver a complete piece of functionality that achieves a specific user or system goal, potentially cutting through all necessary layers (e.g., UI, API, business logic, database).
Focus on "What" and "Why," Not "How":
Stories will primarily focus on the functional outcome, the user value ("what"), and the reason ("why"). Avoid detailing technical implementation ("how") in the story's main description.
The "As a {specific User Persona/system actor}, I want {to perform an action / achieve a goal} so that {I can realize a benefit / achieve a reason}" format is standard. Be precise and consistent when defining the '{specific User Persona/system actor}', ensuring it aligns with defined personas.
Ensure User Value, Not Just Technical Tasks: User Stories must articulate clear user or business value. Avoid creating stories that are purely technical tasks (e.g., "Set up database," "Refactor module X"), unless they are part of the foundational infrastructure Epic or are essential enabling tasks that are explicitly linked to, and justified by, a user-facing story that delivers value.
Appropriate Sizing & Strive for Independence:
Ensure User Stories are appropriately sized for a typical development iteration (i.e., can be completed by the team in one sprint/iteration).
If a vertically sliced story is too large or complex, work with the user to split it into smaller, still valuable, and still vertically sliced increments.
Where feasible, define stories so they can be developed, tested, and potentially delivered independently of others. If dependencies are unavoidable, they must be clearly identified and managed through sequencing.

### IV. Detailing Stories: Comprehensive Acceptance Criteria & Developer Enablement

Clear, Comprehensive, and Testable Acceptance Criteria (ACs):
Every User Story will have detailed, unambiguous, and testable Acceptance Criteria.
ACs precisely define what "done" means for that story from a functional perspective and serve as the basis for verification.
Where a specific Non-Functional Requirement (NFR) from the PRD (e.g., a particular performance target for a specific action, a security constraint for handling certain data) is critical to a story, ensure it is explicitly captured or clearly referenced within its Acceptance Criteria.
Integrate Developer Enablement & Iterative Design into Stories:
Local Testability (CLI): For User Stories involving backend processing or data components, ensure the ACs consider or specify the ability for developers to test that functionality locally (e.g., via CLI commands, local service instances).
Iterative Schema Definition: Database schema changes (new tables, columns) should be introduced iteratively within the User Stories that functionally require them, rather than defining the entire schema upfront.
Upfront UI/UX Standards (if UI applicable): For User Stories with a UI component, ACs should explicitly state requirements regarding look and feel, responsiveness, and adherence to chosen frameworks/libraries (e.g., Tailwind CSS, shadcn/ui) from the start.

### V. Managing Complexity: Addressing Cross-Cutting Concerns Effectively

Critically Evaluate for Cross-Cutting Concerns:
Before finalizing a User Story, evaluate if the described functionality is truly a discrete, user-facing piece of value or if it represents a cross-cutting concern (e.g., a specific logging requirement, a UI theme element used by many views, a core technical enabler for multiple other stories, a specific aspect of error handling).
If a piece of functionality is identified as a cross-cutting concern:
a. Avoid creating a separate User Story for it unless it delivers standalone, testable user value.
b. Instead, integrate the requirement as specific Acceptance Criteria within all relevant User Stories it impacts.
c. Alternatively, if it's a pervasive technical enabler or a non-functional requirement that applies broadly, document it clearly within the relevant PRD section (e.g., 'Non Functional Requirements', 'Technical Assumptions'), or as a note for the Architect within the story descriptions if highly specific.

Your aim is to ensure User Stories remain focused on delivering measurable user value, while still capturing all necessary technical and functional details appropriately.

### VI. Ensuring Quality & Smooth Handoff

Maintain Clarity for Handoff and Architectural Freedom: User Stories, their descriptions, and Acceptance Criteria must be detailed enough to provide the Architect with a clear and comprehensive understanding of "what is required," while allowing for architectural flexibility on the "how."
Confirm "Ready" State: Before considering an Epic's stories complete, ensure each story is effectively "ready" for subsequent architectural review or development planning – meaning it's clear, understandable, testable, its dependencies are noted, and any foundational work (like from the first epic) is accounted for.

## Offer Advanced Self-Refinement & Elicitation Options

(This section is called when needed prior to this)

Present the user with the following list of 'Advanced Reflective, Elicitation & Brainstorming Actions'. Explain that these are optional steps to help ensure quality, explore alternatives, and deepen the understanding of the current section before finalizing it and moving on. The user can select an action by number, or choose to skip this and proceed to finalize the section.

"To ensure the quality of the current section: **[Specific Section Name]** and to ensure its robustness, explore alternatives, and consider all angles, I can perform any of the following actions. Please choose a number (8 to finalize and proceed):

**Advanced Reflective, Elicitation & Brainstorming Actions I Can Take:**

{Instruction for AI Agent: Display the title of each numbered item below. If the user asks what a specific option means, provide a brief explanation of the action you will take, drawing from detailed descriptions tailored for the context.}

1.  **Critical Self-Review & User Goal Alignment**
2.  **Generate & Evaluate Alternative Design Solutions**
3.  **User Journey & Interaction Stress Test (Conceptual)**
4.  **Deep Dive into Design Assumptions & Constraints**
5.  **Usability & Accessibility Audit Review & Probing Questions**
6.  **Collaborative Ideation & UI Feature Brainstorming**
7.  **Elicit 'Unforeseen User Needs' & Future Interaction Questions**
8.  **Finalize this Section and Proceed.**

After I perform the selected action, we can discuss the outcome and decide on any further revisions for this section."

REPEAT by Asking the user if they would like to perform another Reflective, Elicitation & Brainstorming Action UNIT the user indicates it is time to proceed ot the next section (or selects #8)

==================== END: create-prd ====================


==================== START: create-project-brief ====================
# Master Project Brief Creation Task
## System-Level Initiative Planning for Microservices Architecture

## Purpose

- Transform high-level business requirements into comprehensive project briefs for system-wide initiatives
- Define clear project scope, vision, and strategic context for microservices ecosystems
- Provide foundation for Product Manager Master PRD development and cross-service coordination
- Establish framework for platform engineering and AI integration planning

Remember as you follow the upcoming instructions:

- Your project brief must address system-wide concerns and cross-cutting capabilities
- Output will be directly used by Product Manager for Master PRD generation and architectural planning
- Focus on strategic "what" and "why" while setting foundation for detailed "how"
- Ensure alignment with enterprise architecture principles and business strategy

## Instructions

### 1. Determine Project Brief Scope and Type

Before project brief generation, establish:

A. **Project Category:**
   - Platform Engineering Initiative (IDP, developer experience)
   - Business Capability Expansion (new product features across services)
   - Technology Modernization (architecture transformation)
   - AI Integration Project (agentic AI capabilities across ecosystem)
   - Compliance/Security Initiative (enterprise-wide requirements)

B. **System Impact Assessment:**
   - Number of services affected
   - Cross-cutting concerns involved
   - Platform infrastructure changes required
   - Team coordination complexity

### 2. Gather Project Context and Requirements

**Essential Information Collection:**
- Business problem statement and strategic context
- Stakeholder requirements and success criteria
- Technical constraints and architectural principles
- Timeline expectations and resource availability
- Integration requirements with existing systems

**Microservices-Specific Considerations:**
- Service boundary impacts and potential changes
- Cross-service communication requirements
- Data consistency and transaction patterns
- Event-driven architecture implications
- Platform engineering and operational requirements

### 3. Select Appropriate Template

**Template Selection Logic:**
- **System-Wide Projects:** Use `master-project-brief-tmpl` for comprehensive ecosystem initiatives
- **Platform Projects:** Use `master-project-brief-tmpl` with platform engineering focus
- **Legacy Projects:** Use `project-brief-tmpl` for backward compatibility when needed

### 4. Create Comprehensive Project Brief

**Core Sections to Address:**

A. **Executive Summary and Vision**
   - Strategic business context and problem statement
   - Value proposition and competitive advantage
   - High-level solution approach and architecture philosophy

B. **Scope and Objectives**
   - Clear project boundaries and deliverables
   - Success criteria and measurable outcomes
   - Timeline and milestone framework

C. **Microservices Architecture Context**
   - Service ecosystem impact assessment
   - Cross-cutting concerns and platform requirements
   - Integration patterns and communication strategies

D. **AI Integration Strategy** (if applicable)
   - Agentic AI capabilities and placement
   - Human-AI collaboration patterns
   - AI governance and ethics considerations

E. **Platform Engineering Requirements**
   - Developer experience improvements
   - Infrastructure and tooling needs
   - Operational excellence and SRE practices

F. **Implementation Framework**
   - Team topology and organizational alignment
   - Technology stack and architectural decisions
   - Risk assessment and mitigation strategies

### 5. Validate and Refine Brief

**Quality Assurance Checklist:**
- [ ] Clear problem statement and business justification
- [ ] Well-defined scope and success criteria
- [ ] Microservices architecture alignment
- [ ] Cross-service impact assessment
- [ ] Platform engineering considerations
- [ ] AI integration strategy (if applicable)
- [ ] Implementation feasibility and resource requirements
- [ ] Stakeholder alignment and communication plan

### 6. Prepare for PRD Development

**Handoff Preparation:**
- Ensure brief provides sufficient context for Product Manager Master PRD creation
- Include specific guidance for Product Manager agent on PRD development approach
- Identify key stakeholders and decision-making processes
- Establish framework for service-specific brief creation if needed

## Expected Outputs

### Primary Deliverable
Complete Master Project Brief following the `master-project-brief-tmpl` template with:
- Comprehensive project vision and strategic context
- Clear scope definition and success criteria
- Microservices architecture framework
- AI integration strategy and platform requirements
- Implementation roadmap and resource planning

### Secondary Deliverables
- Product Manager agent prompt for Master PRD development
- Framework for individual service brief creation
- Stakeholder communication and alignment plan
- Risk assessment and mitigation framework

## Success Criteria

**Brief Quality Standards:**
- Clear, actionable problem statement and solution vision
- Well-defined scope with measurable success criteria
- Comprehensive microservices architecture context
- Detailed platform engineering and AI integration requirements
- Feasible implementation plan with realistic timelines

**Stakeholder Alignment:**
- All key stakeholders understand and approve project vision
- Clear decision-making processes and communication channels
- Agreed-upon success criteria and measurement framework
- Established governance and oversight mechanisms

**Technical Foundation:**
- Solid architectural foundation for detailed design
- Clear integration patterns and communication strategies
- Platform engineering requirements well-defined
- AI integration strategy aligned with business objectives

## Next Steps

Upon completion of the project brief:

1. **Review and Approval:** Stakeholder review and formal approval process
2. **Master PRD Development:** Hand off to Product Manager agent for detailed requirements
3. **Architecture Planning:** Engage Platform Architect and Service Mesh Architect
4. **Team Coordination:** Establish cross-functional team structure and communication
5. **Service Brief Creation:** Create individual service briefs for affected services

This task ensures comprehensive project planning that aligns with BMAD Method 4.0 principles for enterprise-scale microservices development with integrated AI capabilities.

==================== END: create-project-brief ====================


==================== START: create-service-brief ====================
# Individual Service Brief Creation Task
## Service-Specific Planning and Design for Microservices

## Purpose

- Transform service-level requirements into focused service briefs for individual microservices
- Define clear service scope, responsibilities, and integration patterns
- Provide foundation for Product Manager Service PRD development and implementation planning
- Ensure service alignment with system-wide architecture and business objectives

Remember as you follow the upcoming instructions:

- Your service brief must align with Master Project Brief and system-wide requirements
- Output will be directly used by Product Manager for Service PRD generation and development planning
- Focus on service-specific "what" and "why" while maintaining system coherence
- Ensure clear integration points and dependencies with other services

## Instructions

### 1. Determine Service Brief Scope and Context

Before service brief generation, establish:

A. **Service Type and Category:**
   - Core Business Service (domain logic and business rules)
   - Data Service (data management and persistence)
   - Integration Service (external system connectivity)
   - Platform Service (infrastructure and shared capabilities)
   - AI Agent Service (agentic AI capabilities and orchestration)

B. **Service Context Assessment:**
   - Relationship to Master Project Brief
   - Position in service ecosystem
   - Dependencies on other services
   - Integration complexity and patterns

### 2. Gather Service Requirements and Context

**Essential Information Collection:**
- Service business purpose and value proposition
- Functional requirements and capabilities
- Non-functional requirements (performance, security, scalability)
- Integration requirements with other services
- Data ownership and management responsibilities

**Microservices-Specific Considerations:**
- Service boundary definition and ownership
- Communication patterns (synchronous/asynchronous)
- Data consistency and transaction requirements
- Event publishing and consumption patterns
- Service discovery and registration needs

### 3. Select Appropriate Template

**Template Selection Logic:**
- **Individual Services:** Use `individual-service-brief-tmpl` for focused service planning
- **AI Services:** Use `individual-service-brief-tmpl` with AI-specific considerations
- **Platform Services:** Use `individual-service-brief-tmpl` with platform engineering focus

### 4. Create Comprehensive Service Brief

**Core Sections to Address:**

A. **Service Overview and Purpose**
   - Clear service name and identifier
   - Business purpose and value proposition
   - Service boundaries and responsibilities

B. **Functional Requirements**
   - Core business capabilities and features
   - API specifications and contracts
   - Data processing and business logic

C. **Integration and Communication**
   - Dependencies on other services
   - Communication patterns and protocols
   - Event publishing and consumption
   - External system integrations

D. **Data Management**
   - Data ownership and boundaries
   - Storage requirements and patterns
   - Data consistency and synchronization
   - Privacy and compliance considerations

E. **Non-Functional Requirements**
   - Performance and scalability targets
   - Security and authentication requirements
   - Availability and reliability expectations
   - Monitoring and observability needs

F. **AI Integration** (if applicable)
   - AI agent capabilities and placement
   - Machine learning model integration
   - Human-AI collaboration patterns
   - AI governance and ethics compliance

### 5. Define Service Architecture Context

**Technical Architecture Considerations:**
- Technology stack and framework selection
- Deployment and containerization strategy
- Configuration management and secrets
- Service mesh integration patterns

**Operational Requirements:**
- Monitoring and alerting specifications
- Logging and tracing requirements
- Health check and readiness probes
- Backup and disaster recovery procedures

### 6. Validate Service Brief Quality

**Quality Assurance Checklist:**
- [ ] Clear service purpose and business value
- [ ] Well-defined service boundaries and responsibilities
- [ ] Comprehensive functional requirements
- [ ] Clear integration patterns and dependencies
- [ ] Non-functional requirements specified
- [ ] Data ownership and management defined
- [ ] AI integration strategy (if applicable)
- [ ] Operational and monitoring requirements
- [ ] Alignment with system-wide architecture

### 7. Prepare for Service PRD Development

**Handoff Preparation:**
- Ensure brief provides sufficient context for Product Manager Service PRD creation
- Include specific guidance for Product Manager agent on PRD development approach
- Identify service-specific stakeholders and requirements
- Establish framework for technical design and implementation

## Expected Outputs

### Primary Deliverable
Complete Individual Service Brief following the `individual-service-brief-tmpl` template with:
- Clear service definition and business purpose
- Comprehensive functional and non-functional requirements
- Integration patterns and communication specifications
- Data management and ownership boundaries
- AI integration strategy and operational requirements

### Secondary Deliverables
- Product Manager agent prompt for Service PRD development
- Service integration contract specifications
- Technical architecture guidance and constraints
- Implementation planning framework

## Success Criteria

**Brief Quality Standards:**
- Clear, focused service definition with well-defined boundaries
- Comprehensive requirements covering all service aspects
- Clear integration patterns and dependency management
- Feasible implementation plan with realistic constraints
- Alignment with system-wide architecture and standards

**Service Alignment:**
- Service purpose aligns with business objectives
- Clear value proposition and ownership model
- Integration patterns support system-wide goals
- Non-functional requirements meet enterprise standards

**Technical Foundation:**
- Solid foundation for detailed technical design
- Clear API contracts and communication patterns
- Data management strategy well-defined
- Operational requirements comprehensive and measurable

## Next Steps

Upon completion of the service brief:

1. **Review and Validation:** Technical and business stakeholder review
2. **Service PRD Development:** Hand off to Product Manager agent for detailed requirements
3. **Technical Design:** Engage Service Mesh Architect for integration design
4. **Implementation Planning:** Establish development team and timeline
5. **Integration Coordination:** Coordinate with dependent services and teams

This task ensures focused service planning that aligns with BMAD Method 4.0 principles for enterprise-scale microservices development while maintaining clear service boundaries and responsibilities.

==================== END: create-service-brief ====================


==================== START: create-service-integration-contract ====================
# Service Integration Contract Creation Task
## Cross-Service Communication and Dependency Management

## Purpose

- Define clear communication contracts between microservices
- Establish API specifications and event schemas for service integration
- Manage cross-service dependencies and coordination requirements
- Ensure reliable and maintainable service-to-service communication

Remember as you follow the upcoming instructions:

- Your contracts form the foundation for reliable microservices communication
- Output will be used by development teams to implement service integrations
- Focus on clear specifications that prevent integration issues
- Ensure contracts support service evolution and backward compatibility

## Instructions

### 1. Determine Integration Context

Before contract creation, establish the integration context:

A. **Integration Type Assessment:**
   - **Synchronous Integration:** Direct API calls requiring immediate response
   - **Asynchronous Integration:** Event-driven communication with eventual consistency
   - **Hybrid Integration:** Combination of both synchronous and asynchronous patterns

B. **Service Relationship Analysis:**
   - **Provider Service:** Service that exposes functionality or publishes events
   - **Consumer Service:** Service that calls APIs or consumes events
   - **Bidirectional:** Services that both provide and consume from each other

C. **Business Context Understanding:**
   - **Business Process:** What business workflow requires this integration
   - **Data Flow:** What information needs to be shared between services
   - **Timing Requirements:** Real-time vs. eventual consistency requirements

### 2. Review Integration Requirements

Review all available inputs:
- Service PRDs for both provider and consumer services
- System architecture documentation
- Business process requirements
- Performance and reliability requirements
- Security and compliance constraints

### 3. Choose Contract Creation Approach

Select appropriate approach based on integration complexity:

A. **Simple API Contract:** Straightforward request-response integration
B. **Event-Driven Contract:** Asynchronous event publishing and consumption
C. **Complex Integration Contract:** Multiple communication patterns and dependencies
D. **Legacy Integration Contract:** Integration with existing legacy systems

### 4. Process Contract Sections

Work through the contract sections using the `service-integration-contract-tmpl` template:

#### 4A. Integration Overview and Communication Patterns
- **Integration Purpose:** Clear business justification for the integration
- **Communication Pattern:** Synchronous, asynchronous, or hybrid approach
- **Integration Type:** Data sharing, workflow coordination, event notification, or command execution
- **Service Relationship:** Provider-consumer dynamics and responsibilities

#### 4B. API Contract Specifications (for Synchronous Integration)
- **Endpoint Definitions:** Complete REST API specifications with HTTP methods
- **Request/Response Schemas:** JSON schemas with validation rules and examples
- **Error Handling Contract:** Standardized error response formats and codes
- **Authentication and Authorization:** Security requirements and access control

#### 4C. Event Contract Specifications (for Asynchronous Integration)
- **Event Schema Definitions:** Complete event structure with required and optional fields
- **Event Publishing Contract:** Topic/queue configuration and publishing guarantees
- **Event Consumption Contract:** Processing requirements and error handling
- **Event Evolution:** Versioning strategy and backward compatibility

### 5. Define Data Consistency and Transaction Boundaries

#### 5A. Consistency Model Selection
- **Strong Consistency:** Immediate consistency across services (use sparingly)
- **Eventual Consistency:** Consistency achieved over time (preferred for microservices)
- **Causal Consistency:** Causally related operations maintain consistency

#### 5B. Transaction Coordination Strategy
- **No Distributed Transactions:** Each service manages its own transactions (preferred)
- **Saga Pattern:** Distributed transaction coordination with compensation actions
- **Two-Phase Commit:** Traditional distributed transactions (avoid if possible)

#### 5C. Compensation and Rollback Procedures
- **Compensation Actions:** Define rollback procedures for failed distributed transactions
- **Idempotency Requirements:** Ensure operations can be safely retried
- **Error Recovery:** Procedures for handling partial failures

### 6. Performance and Reliability Requirements

#### 6A. Performance Targets
- **Response Time:** Target latency for synchronous calls (e.g., < 200ms for 95th percentile)
- **Throughput:** Expected requests per second or events per minute
- **Availability:** Target uptime and reliability requirements (e.g., 99.9%)

#### 6B. Reliability Patterns
- **Circuit Breaker:** Fail fast when downstream service is unavailable
- **Retry with Backoff:** Retry failed requests with exponential backoff
- **Timeout Configuration:** Maximum wait time for responses
- **Bulkhead Pattern:** Resource isolation to prevent cascade failures

#### 6C. Fallback Strategies
- **Graceful Degradation:** How service continues with limited functionality
- **Default Values:** Default responses when service is unavailable
- **Cached Responses:** Use of cached data during outages

### 7. Security and Compliance Requirements

#### 7A. Authentication and Authorization
- **Service-to-Service Authentication:** JWT tokens, mTLS, or API keys
- **Authorization Rules:** What operations each service is authorized to perform
- **Token Management:** Token lifecycle and refresh procedures

#### 7B. Data Protection and Privacy
- **Encryption Requirements:** TLS for transit, encryption for sensitive data at rest
- **Data Masking:** PII and sensitive data protection
- **Audit Logging:** Security event logging and compliance tracking
- **Data Retention:** Data lifecycle and deletion policies

### 8. Monitoring and Observability

#### 8A. Metrics and Monitoring
- **Key Metrics:** Request count, response time, error rate, throughput, availability
- **Distributed Tracing:** Trace ID propagation and span creation
- **Business Metrics:** Domain-specific metrics for business monitoring

#### 8B. Alerting and Incident Response
- **Alerting Rules:** Thresholds for error rates, latency, and availability
- **Escalation Procedures:** Who to contact when integration fails
- **Troubleshooting Guides:** Common issues and resolution procedures

### 9. Testing and Validation Strategy

#### 9A. Contract Testing
- **Consumer-Driven Contracts:** Consumer defines expected contract behavior
- **Provider Contract Tests:** Provider validates contract compliance
- **Contract Evolution Tests:** Backward compatibility validation

#### 9B. Integration Testing
- **End-to-End Tests:** Full workflow testing across service boundaries
- **Component Tests:** Service boundary and integration point testing
- **Chaos Testing:** Failure scenario and resilience testing

#### 9C. Performance Testing
- **Load Testing:** Validate performance under expected load
- **Stress Testing:** Test behavior under extreme conditions
- **Latency Testing:** Measure end-to-end processing time

### 10. Versioning and Evolution Strategy

#### 10A. API Versioning
- **Versioning Strategy:** URL versioning, header versioning, or content negotiation
- **Backward Compatibility:** How to maintain compatibility during evolution
- **Deprecation Policy:** Timeline and procedures for deprecating old versions

#### 10B. Schema Evolution
- **Additive Changes:** Adding new optional fields (non-breaking)
- **Breaking Changes:** Field removal or type changes (requires version bump)
- **Migration Strategy:** How consumers migrate to new versions

### 11. Operational Procedures

#### 11A. Deployment Coordination
- **Deployment Order:** Which service should be deployed first
- **Rollback Procedures:** How to rollback if integration fails
- **Zero-Downtime Deployment:** Blue-green or canary deployment strategies

#### 11B. Incident Management
- **Communication Protocol:** How teams communicate during incidents
- **Escalation Path:** Clear escalation procedures for integration issues
- **Post-Incident Review:** Learning and improvement procedures

### 12. Contract Review and Approval

#### 12A. Technical Review
- **Provider Team Review:** Technical validation by the provider service team
- **Consumer Team Review:** Requirements validation by the consumer service team
- **Architecture Review:** System-wide architecture and pattern compliance

#### 12B. Business and Operational Review
- **Product Owner Approval:** Business value and requirement alignment
- **DevOps/SRE Review:** Operational feasibility and monitoring requirements
- **Security Review:** Security controls and compliance validation

## Deliverables

### Primary Deliverable
Complete Service Integration Contract following the `service-integration-contract-tmpl` template with:
- Clear integration specifications and communication patterns
- Comprehensive API or event contract definitions
- Performance, reliability, and security requirements
- Testing strategy and validation procedures
- Operational procedures and monitoring requirements

### Secondary Deliverables
- Event schema definitions (if applicable) using `event-schema-definition-tmpl`
- Integration testing strategy and test cases
- Monitoring and alerting configuration requirements
- Documentation for development teams

## Success Criteria

- Contract provides clear and unambiguous integration specifications
- All communication patterns and data formats are well-defined
- Performance and reliability requirements are realistic and measurable
- Security and compliance requirements are properly addressed
- Testing strategy ensures integration reliability and maintainability
- Operational procedures support reliable service communication
- Contract supports service evolution and backward compatibility

## Validation Checklist

- [ ] Integration purpose and business value clearly defined
- [ ] Communication patterns appropriate for use case
- [ ] API or event contracts are complete and well-specified
- [ ] Error handling and fallback strategies are defined
- [ ] Performance and reliability requirements are measurable
- [ ] Security requirements are comprehensive and implementable
- [ ] Testing strategy covers functional and non-functional requirements
- [ ] Monitoring and alerting requirements are specified
- [ ] Versioning and evolution strategy supports maintainability
- [ ] All stakeholders have reviewed and approved the contract

==================== END: create-service-integration-contract ====================


==================== START: create-service-prd ====================
# Individual Service PRD Generation Task
## Detailed Service Requirements and Technical Specifications

## Purpose

- Transform Service Brief (created by Analyst) into detailed service-specific product requirements
- Define comprehensive technical specifications for individual microservices
- Provide foundation for service development teams and technical implementation
- Ensure service aligns with system-wide requirements and architectural principles

Remember as you follow the upcoming instructions:

- Your service PRD must align with the Master Project PRD and system-wide requirements
- Output will be directly used by development teams for service implementation
- Focus on service-specific "what" and "how" while maintaining system coherence
- Ensure clear integration points and dependencies with other services
- You are transforming strategic service briefs from Analyst into actionable service requirements

## Instructions

### 1. Define Service Context and Scope

Before Service PRD generation, establish:

A. **Service Type and Category:**
   - Core Business Service
   - Data Service
   - Integration Service
   - Platform Service
   - AI Agent Service

B. **Service Boundaries and Responsibilities:**
   - Clear definition of service ownership
   - Business capability alignment
   - Data ownership boundaries
   - Integration responsibilities

C. **System Integration Context:**
   - Dependencies on other services
   - Services that depend on this service
   - Cross-cutting concerns and shared requirements

### 2. Review Service-Level Inputs

Review all available inputs:
- Individual Service Brief (created by Analyst)
- Master Project PRD (system-wide requirements)
- Service catalog and dependency matrix
- Platform requirements and constraints
- AI integration requirements (if applicable)

### 3. Determine Service Complexity and Approach

Assess service complexity and choose appropriate approach:

A. **Simple Service Approach:** Straightforward CRUD operations with minimal business logic
B. **Complex Business Service:** Rich domain logic with multiple integration points
C. **AI-Enhanced Service:** Service with integrated AI agent capabilities
D. **Platform Service:** Infrastructure or cross-cutting service for other services

### 4. Process Service PRD Sections

Work through the Service PRD sections using the `individual-service-prd-tmpl` template:

#### 4A. Service Definition and Business Context
- **Service Mission and Purpose**: Clear business capability and value proposition
- **Business Domain Alignment**: Bounded context and domain model integration
- **Service Boundaries**: Responsibilities, ownership, and scope limitations
- **Stakeholders and Users**: Primary consumers and stakeholders

#### 4B. Functional Requirements
- **Core Capabilities**: Primary service functions and business logic
- **API Specifications**: Detailed endpoint specifications with schemas
- **Data Operations**: CRUD operations, queries, and data processing
- **Business Logic**: Algorithms, calculations, and decision processes

#### 4C. Non-Functional Requirements
- **Performance Requirements**: Latency, throughput, and response time targets
- **Scalability Requirements**: Horizontal scaling and load handling capacity
- **Reliability Requirements**: Uptime, fault tolerance, and error recovery
- **Security Requirements**: Authentication, authorization, and data protection

### 5. API Design and Contract Specifications

#### 5A. RESTful API Design
Create comprehensive API specifications including:
- **Endpoint Definitions**: Complete REST endpoints with HTTP methods
- **Request/Response Schemas**: JSON schemas with validation rules
- **Error Handling**: Error codes, messages, and recovery procedures
- **API Versioning**: Evolution strategy and backward compatibility

#### 5B. Integration Contracts
Define integration patterns:
- **Synchronous Communication**: Direct API calls and request-response patterns
- **Asynchronous Communication**: Event publishing and consumption patterns
- **External Integrations**: Third-party APIs and legacy system connections
- **Service Discovery**: Registration and discovery mechanisms

### 6. Data Model and Storage Design

#### 6A. Data Architecture
- **Entity Definitions**: Data models, relationships, and constraints
- **Database Schema**: Table structures, indexes, and optimization
- **Data Validation**: Input validation, business rules, and integrity checks
- **Data Lifecycle**: Creation, updates, archival, and deletion policies

#### 6B. Data Integration
- **Data Ownership**: Service-specific data boundaries and responsibilities
- **Data Sharing**: Cross-service data access patterns and protocols
- **Data Consistency**: Consistency models and transaction boundaries
- **Data Migration**: Migration strategies and data evolution

### 7. AI Integration (if applicable)

#### 7A. AI Agent Capabilities
- **Agent Integration Points**: Where and how AI agents interact with the service
- **AI Workflows**: Automated processes and decision-making capabilities
- **Model Integration**: AI model usage, inference, and scaling requirements
- **Human-AI Collaboration**: Handoff procedures and escalation protocols

#### 7B. AI Infrastructure Requirements
- **Vector Database Integration**: Embedding storage and semantic search
- **Model Serving**: Inference endpoints and scaling requirements
- **Context Management**: Memory and state preservation across interactions
- **AI Observability**: Monitoring, evaluation, and performance tracking

### 8. Security and Compliance

#### 8A. Security Architecture
- **Authentication**: Service-to-service and user authentication mechanisms
- **Authorization**: Role-based access control and permission management
- **Data Protection**: Encryption, privacy, and data handling procedures
- **Security Monitoring**: Threat detection and incident response

#### 8B. Compliance Requirements
- **Regulatory Compliance**: Industry-specific and regulatory requirements
- **Audit Trail**: Logging, tracking, and compliance reporting
- **Data Privacy**: Privacy-by-design and data minimization principles
- **Quality Assurance**: Validation procedures and quality controls

### 9. Testing and Quality Assurance

#### 9A. Testing Strategy
- **Unit Testing**: Component testing approach and coverage requirements
- **Integration Testing**: Service interaction and contract validation
- **Performance Testing**: Load testing, stress testing, and optimization
- **Security Testing**: Vulnerability assessment and penetration testing

#### 9B. Quality Gates
- **Code Quality**: Standards, reviews, and automated validation
- **Performance Validation**: Response time and throughput verification
- **Security Validation**: Security controls and vulnerability assessment
- **Compliance Validation**: Regulatory requirements and audit readiness

### 10. Deployment and Operations

#### 10A. Deployment Strategy
- **Containerization**: Docker configuration and image management
- **Orchestration**: Kubernetes deployment and service configuration
- **CI/CD Pipeline**: Build, test, and deployment automation
- **Environment Management**: Development, staging, and production configuration

#### 10B. Operational Requirements
- **Monitoring and Observability**: Health checks, metrics, and alerting
- **Scaling and Performance**: Auto-scaling policies and resource management
- **Maintenance and Updates**: Update procedures and maintenance windows
- **Incident Response**: Error handling, recovery, and escalation procedures

### 11. Service Dependencies and Integration

#### 11A. Dependency Management
- **Service Dependencies**: Required upstream services and external APIs
- **Downstream Consumers**: Services and applications that depend on this service
- **Dependency Coordination**: Version management and compatibility requirements
- **Fallback Strategies**: Graceful degradation and error handling

#### 11B. Integration Patterns
- **Event-Driven Integration**: Event publishing and consumption patterns
- **API Integration**: Synchronous service-to-service communication
- **Data Integration**: Shared data access and consistency patterns
- **External Integration**: Third-party services and legacy system connectivity

### 12. Performance and Scalability

#### 12A. Performance Requirements
- **Response Time Targets**: Latency requirements for different operations
- **Throughput Requirements**: Request handling capacity and load expectations
- **Resource Utilization**: CPU, memory, and storage optimization
- **Performance Monitoring**: Metrics collection and performance tracking

#### 12B. Scalability Strategy
- **Horizontal Scaling**: Auto-scaling policies and scaling triggers
- **Vertical Scaling**: Resource allocation and capacity planning
- **Load Distribution**: Load balancing and traffic management
- **Capacity Planning**: Growth projections and resource allocation

### 13. Documentation and Knowledge Management

#### 13A. Technical Documentation
- **API Documentation**: Comprehensive endpoint documentation and examples
- **Architecture Documentation**: Service design and technical specifications
- **Operational Documentation**: Runbooks and troubleshooting guides
- **Development Documentation**: Setup, configuration, and development guides

#### 13B. Knowledge Transfer
- **Team Onboarding**: New team member onboarding materials
- **Knowledge Sharing**: Documentation standards and knowledge base
- **Training Materials**: Service-specific training and education resources
- **Support Documentation**: User guides and support procedures

### 14. Validation and Review

#### 14A. Service Coherence Validation
- **System Alignment**: Ensure service aligns with system-wide requirements
- **Integration Completeness**: Verify all integration points are addressed
- **Quality Completeness**: Ensure all quality requirements are covered
- **Operational Readiness**: Validate deployment and operational procedures

#### 14B. Stakeholder Review
- **Technical Review**: Architecture and implementation feasibility
- **Business Review**: Business value and requirement alignment
- **Operational Review**: Deployment and maintenance feasibility
- **Security Review**: Security controls and compliance validation

## Deliverables

### Primary Deliverable
Complete Individual Service PRD following the `individual-service-prd-tmpl` template with:
- Comprehensive service-specific requirements
- Detailed API specifications and contracts
- Data model and storage requirements
- Integration patterns and dependencies
- Testing and quality assurance procedures
- Deployment and operational specifications

### Secondary Deliverables
- Service integration specifications
- API contract documentation
- Testing strategy and procedures
- Deployment and operational guides

## Success Criteria

- Service PRD provides clear and comprehensive service specifications
- All functional and non-functional requirements are properly defined
- API contracts are complete and well-documented
- Integration points and dependencies are clearly specified
- Testing strategy ensures service quality and reliability
- Deployment and operational procedures are well-defined
- Service aligns with system-wide architecture and requirements

==================== END: create-service-prd ====================


==================== START: create-uxui-spec ====================
# Create UI/UX Specification Task

## Purpose

To collaboratively work with the user to define and document the User Interface (UI) and User Experience (UX) specifications for the project. This involves understanding user needs, defining information architecture, outlining user flows, and ensuring a solid foundation for visual design and frontend development. The output will populate a new document called `front-end-spec.md` following the `front-end-spec-tmpl` template.

## Inputs

- Project Brief (`project-brief.md` or equivalent)
- Product Requirements Document (PRD) (`prd.md` or equivalent)
- User feedback or research (if available)

## Key Activities & Instructions

### 1. Understand Core Requirements

- Review Project Brief and PRD to grasp project goals, target audience, key features, and any existing constraints.
- Ask clarifying questions about user needs, pain points, and desired outcomes.

### 2. Define Overall UX Goals & Principles (for `front-end-spec-tmpl`)

- Collaboratively establish and document:
  - Target User Personas (elicit details or confirm existing ones).
  - Key Usability Goals.
  - Core Design Principles for the project.

### 3. Develop Information Architecture (IA) (for `front-end-spec-tmpl`)

- Work with the user to create a Site Map or Screen Inventory.
- Define the primary and secondary Navigation Structure.
- Use Mermaid diagrams or lists as appropriate for the template.

### 4. Outline Key User Flows (for `front-end-spec-tmpl`)

- Identify critical user tasks from the PRD/brief.
- For each flow:
  - Define the user's goal.
  - Collaboratively map out the steps (use Mermaid diagrams or detailed step-by-step descriptions).
  - Consider edge cases and error states.

### 5. Discuss Wireframes & Mockups Strategy (for `front-end-spec-tmpl`)

- Clarify where detailed visual designs will be created (e.g., Figma, Sketch) and ensure the `front-end-spec-tmpl` correctly links to these primary design files.
- If low-fidelity wireframes are needed first, offer to help conceptualize layouts for key screens.

### 6. Define Component Library / Design System Approach (for `front-end-spec-tmpl`)

- Discuss if an existing design system will be used or if a new one needs to be developed.
- If new, identify a few foundational components to start with (e.g., Button, Input, Card) and their key states/behaviors at a high level. Detailed technical specs will be in `front-end-architecture`.

### 7. Establish Branding & Style Guide Basics (for `front-end-spec-tmpl`)

- If a style guide exists, link to it.
- If not, collaboratively define placeholders for: Color Palette, Typography, Iconography, Spacing.

### 8. Specify Accessibility (AX) Requirements (for `front-end-spec-tmpl`)

- Determine the target compliance level (e.g., WCAG 2.1 AA).
- List any known specific AX requirements.

### 9. Define Responsiveness Strategy (for `front-end-spec-tmpl`)

- Discuss and document key Breakpoints.
- Describe the general Adaptation Strategy.

### 10. Output Generation & Iterative Refinement (Guided by `front-end-spec-tmpl`)

- **a. Draft Section:** Incrementally populate one logical section of the `front-end-spec-tmpl` file based on your discussions.
- **b. Present & Incorporate Initial Feedback:** Present the drafted section to the user for review. Discuss, explain and incorporate their initial feedback and revisions directly.
- **c. [Offer Advanced Self-Refinement & Elicitation Options](#offer-advanced-self-refinement--elicitation-options)**

## Offer Advanced Self-Refinement & Elicitation Options

(This section is called when needed prior to this)

Present the user with the following list of 'Advanced Reflective, Elicitation & Brainstorming Actions'. Explain that these are optional steps to help ensure quality, explore alternatives, and deepen the understanding of the current section before finalizing it and moving on. The user can select an action by number, or choose to skip this and proceed to finalize the section.

"To ensure the quality of the current section: **[Specific Section Name]** and to ensure its robustness, explore alternatives, and consider all angles, I can perform any of the following actions. Please choose a number (8 to finalize and proceed):

**Advanced Reflective, Elicitation & Brainstorming Actions I Can Take:**

{Instruction for AI Agent: Display the title of each numbered item below. If the user asks what a specific option means, provide a brief explanation of the action you will take, drawing from detailed descriptions tailored for the context.}

1.  **Critical Self-Review & User Goal Alignment**
2.  **Generate & Evaluate Alternative Design Solutions**
3.  **User Journey & Interaction Stress Test (Conceptual)**
4.  **Deep Dive into Design Assumptions & Constraints**
5.  **Usability & Accessibility Audit Review & Probing Questions**
6.  **Collaborative Ideation & UI Feature Brainstorming**
7.  **Elicit 'Unforeseen User Needs' & Future Interaction Questions**
8.  **Finalize this Section and Proceed.**

After I perform the selected action, we can discuss the outcome and decide on any further revisions for this section."

REPEAT by Asking the user if they would like to perform another Reflective, Elicitation & Brainstorming Action UNIT the user indicates it is time to proceed ot the next section (or selects #8)

==================== END: create-uxui-spec ====================


==================== START: design-system-integration-strategy ====================
# Design System Integration Strategy Task

## Objective
Develop a comprehensive strategy for integrating a unified design system across multiple microfrontends to ensure consistent user experience, efficient development workflows, and maintainable design standards.

## Context
You are creating an integration strategy that enables multiple autonomous teams to use a shared design system while maintaining their independence and ability to innovate within established design guidelines.

## Prerequisites
- Review microfrontend architecture and team structure
- Understand current design assets and brand guidelines
- Assess existing component libraries and design tools
- Identify user experience requirements and accessibility standards
- Review technology stack and framework choices

## Task Instructions

### 1. Design System Architecture Strategy

#### Centralized vs. Federated Approach
Define the distribution model:

```markdown
# Design System Distribution Strategy

## Centralized Distribution Model
### NPM Package Distribution
**Benefits**:
- Single source of truth for design components
- Versioned releases with semantic versioning
- Dependency management through package managers
- Automated distribution and updates

**Implementation**:
```json
{
  "name": "@company/design-system",
  "version": "2.1.0",
  "description": "Company Design System Components",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "exports": {
    ".": "./dist/index.js",
    "./tokens": "./dist/tokens/index.js",
    "./components": "./dist/components/index.js",
    "./themes": "./dist/themes/index.js",
    "./utils": "./dist/utils/index.js"
  },
  "peerDependencies": {
    "react": ">=18.0.0",
    "react-dom": ">=18.0.0"
  },
  "files": [
    "dist/",
    "README.md",
    "CHANGELOG.md"
  ]
}
```

### Module Federation Distribution
**Benefits**:
- Runtime sharing of design system components
- Reduced bundle duplication across microfrontends
- Dynamic updates without redeployment
- Framework-agnostic component sharing

**Implementation**:
```javascript
// Design System Module Federation Config
const ModuleFederationPlugin = require('@module-federation/webpack');

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      name: 'designSystem',
      filename: 'designSystemEntry.js',
      exposes: {
        './Button': './src/components/Button',
        './Input': './src/components/Input',
        './Card': './src/components/Card',
        './Modal': './src/components/Modal',
        './tokens': './src/tokens',
        './themes': './src/themes',
      },
      shared: {
        react: { singleton: true, eager: true },
        'react-dom': { singleton: true, eager: true },
      },
    }),
  ],
};

// Microfrontend consumption
const ModuleFederationPlugin = require('@module-federation/webpack');

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      name: 'userMicrofrontend',
      remotes: {
        designSystem: 'designSystem@https://cdn.company.com/design-system/designSystemEntry.js',
      },
      shared: {
        react: { singleton: true },
        'react-dom': { singleton: true },
      },
    }),
  ],
};
```

## Design Token Management
### Token Architecture
```typescript
// Design token structure
interface DesignTokens {
  colors: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  shadows: ShadowTokens;
  borderRadius: BorderRadiusTokens;
  transitions: TransitionTokens;
  breakpoints: BreakpointTokens;
}

interface ColorTokens {
  brand: {
    primary: ColorScale;
    secondary: ColorScale;
    tertiary: ColorScale;
  };
  semantic: {
    success: ColorScale;
    warning: ColorScale;
    error: ColorScale;
    info: ColorScale;
  };
  neutral: ColorScale;
  surface: {
    background: string;
    foreground: string;
    muted: string;
    accent: string;
  };
}

interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}
```

### Token Distribution Strategy
```typescript
// Token generation and distribution
class TokenManager {
  private tokens: DesignTokens;
  
  constructor(tokens: DesignTokens) {
    this.tokens = tokens;
  }
  
  // Generate CSS custom properties
  generateCSSVariables(): string {
    const cssVars: string[] = [];
    
    // Generate color variables
    Object.entries(this.tokens.colors.brand).forEach(([name, scale]) => {
      Object.entries(scale).forEach(([shade, value]) => {
        cssVars.push(`--color-${name}-${shade}: ${value};`);
      });
    });
    
    // Generate typography variables
    Object.entries(this.tokens.typography.fontSize).forEach(([name, value]) => {
      cssVars.push(`--font-size-${name}: ${value};`);
    });
    
    return `:root {\n  ${cssVars.join('\n  ')}\n}`;
  }
  
  // Generate Tailwind CSS configuration
  generateTailwindConfig(): object {
    return {
      theme: {
        extend: {
          colors: this.flattenColorTokens(this.tokens.colors),
          fontSize: this.tokens.typography.fontSize,
          fontFamily: this.tokens.typography.fontFamily,
          spacing: this.tokens.spacing,
          borderRadius: this.tokens.borderRadius,
          boxShadow: this.tokens.shadows,
          screens: this.tokens.breakpoints,
        },
      },
    };
  }
  
  // Generate JavaScript/TypeScript tokens
  generateJSTokens(): string {
    return `export const tokens = ${JSON.stringify(this.tokens, null, 2)};`;
  }
  
  private flattenColorTokens(colors: any, prefix = ''): Record<string, string> {
    const flattened: Record<string, string> = {};
    
    Object.entries(colors).forEach(([key, value]) => {
      const newKey = prefix ? `${prefix}-${key}` : key;
      
      if (typeof value === 'string') {
        flattened[newKey] = value;
      } else if (typeof value === 'object') {
        Object.assign(flattened, this.flattenColorTokens(value, newKey));
      }
    });
    
    return flattened;
  }
}
```

## Component Library Strategy
### Component Architecture
```typescript
// Base component interface
interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  testId?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

// Component variant system
interface VariantProps {
  variant?: 'primary' | 'secondary' | 'tertiary' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  state?: 'default' | 'hover' | 'active' | 'disabled' | 'loading';
}

// Button component example
interface ButtonProps extends BaseComponentProps, VariantProps {
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  href?: string;
  target?: string;
  rel?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    variant = 'primary', 
    size = 'medium', 
    disabled = false,
    loading = false,
    className,
    children,
    testId,
    ...props 
  }, ref) => {
    const buttonClasses = cn(
      'btn',
      `btn--${variant}`,
      `btn--${size}`,
      {
        'btn--disabled': disabled,
        'btn--loading': loading,
      },
      className
    );
    
    if (props.href && !disabled) {
      return (
        <a
          ref={ref as any}
          className={buttonClasses}
          data-testid={testId}
          {...(props as any)}
        >
          {loading && <Spinner size="small" />}
          {children}
        </a>
      );
    }
    
    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        data-testid={testId}
        {...props}
      >
        {loading && <Spinner size="small" />}
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';
```

### Component Documentation Strategy
```typescript
// Storybook configuration for component documentation
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Button component for user interactions with multiple variants and states.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'tertiary', 'ghost'],
      description: 'Visual style variant of the button',
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
      description: 'Size of the button',
    },
    disabled: {
      control: { type: 'boolean' },
      description: 'Whether the button is disabled',
    },
    loading: {
      control: { type: 'boolean' },
      description: 'Whether the button is in loading state',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Button',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="flex gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="tertiary">Tertiary</Button>
      <Button variant="ghost">Ghost</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All available button variants displayed together.',
      },
    },
  },
};

export const AllSizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="small">Small</Button>
      <Button size="medium">Medium</Button>
      <Button size="large">Large</Button>
    </div>
  ),
};

export const States: Story = {
  render: () => (
    <div className="flex gap-4">
      <Button>Default</Button>
      <Button disabled>Disabled</Button>
      <Button loading>Loading</Button>
    </div>
  ),
};
```

## Theme Management Strategy
### Multi-Theme Support
```typescript
// Theme configuration
interface Theme {
  name: string;
  colors: ThemeColors;
  typography: ThemeTypography;
  spacing: ThemeSpacing;
  shadows: ThemeShadows;
  borderRadius: ThemeBorderRadius;
}

interface ThemeColors {
  background: string;
  foreground: string;
  primary: string;
  secondary: string;
  muted: string;
  accent: string;
  destructive: string;
  border: string;
  input: string;
  ring: string;
}

// Light theme
const lightTheme: Theme = {
  name: 'light',
  colors: {
    background: 'hsl(0 0% 100%)',
    foreground: 'hsl(222.2 84% 4.9%)',
    primary: 'hsl(221.2 83.2% 53.3%)',
    secondary: 'hsl(210 40% 96%)',
    muted: 'hsl(210 40% 96%)',
    accent: 'hsl(210 40% 96%)',
    destructive: 'hsl(0 84.2% 60.2%)',
    border: 'hsl(214.3 31.8% 91.4%)',
    input: 'hsl(214.3 31.8% 91.4%)',
    ring: 'hsl(221.2 83.2% 53.3%)',
  },
  // ... other theme properties
};

// Dark theme
const darkTheme: Theme = {
  name: 'dark',
  colors: {
    background: 'hsl(222.2 84% 4.9%)',
    foreground: 'hsl(210 40% 98%)',
    primary: 'hsl(217.2 91.2% 59.8%)',
    secondary: 'hsl(217.2 32.6% 17.5%)',
    muted: 'hsl(217.2 32.6% 17.5%)',
    accent: 'hsl(217.2 32.6% 17.5%)',
    destructive: 'hsl(0 62.8% 30.6%)',
    border: 'hsl(217.2 32.6% 17.5%)',
    input: 'hsl(217.2 32.6% 17.5%)',
    ring: 'hsl(224.3 76.3% 94.1%)',
  },
  // ... other theme properties
};

// Theme provider
const ThemeContext = React.createContext<{
  theme: Theme;
  setTheme: (theme: Theme) => void;
  themes: Theme[];
}>({
  theme: lightTheme,
  setTheme: () => {},
  themes: [lightTheme, darkTheme],
});

export const ThemeProvider: React.FC<{
  children: React.ReactNode;
  defaultTheme?: Theme;
  themes?: Theme[];
}> = ({ 
  children, 
  defaultTheme = lightTheme, 
  themes = [lightTheme, darkTheme] 
}) => {
  const [theme, setTheme] = React.useState(defaultTheme);
  
  // Apply theme CSS variables
  React.useEffect(() => {
    const root = document.documentElement;
    
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--${key}`, value);
    });
    
    // Add theme class to body
    document.body.className = document.body.className
      .replace(/theme-\w+/g, '')
      .concat(` theme-${theme.name}`)
      .trim();
  }, [theme]);
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme, themes }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = React.useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
```

## Integration Testing Strategy
### Component Testing
```typescript
// Component integration tests
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '../providers/ThemeProvider';
import { Button } from '../components/Button';

const renderWithTheme = (component: React.ReactElement, theme?: Theme) => {
  return render(
    <ThemeProvider defaultTheme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('Button Component Integration', () => {
  it('renders with correct theme styles', () => {
    renderWithTheme(<Button variant="primary">Test Button</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('btn', 'btn--primary');
  });
  
  it('applies dark theme correctly', () => {
    renderWithTheme(
      <Button variant="primary">Test Button</Button>,
      darkTheme
    );
    
    const button = screen.getByRole('button');
    const styles = window.getComputedStyle(button);
    expect(styles.getPropertyValue('--background')).toBe(darkTheme.colors.background);
  });
  
  it('handles theme switching', () => {
    const ThemeToggle = () => {
      const { theme, setTheme, themes } = useTheme();
      return (
        <div>
          <Button variant="primary">Themed Button</Button>
          <button 
            onClick={() => setTheme(theme.name === 'light' ? themes[1] : themes[0])}
          >
            Toggle Theme
          </button>
        </div>
      );
    };
    
    renderWithTheme(<ThemeToggle />);
    
    const toggleButton = screen.getByText('Toggle Theme');
    fireEvent.click(toggleButton);
    
    // Verify theme change
    expect(document.body).toHaveClass('theme-dark');
  });
});
```

### Visual Regression Testing
```typescript
// Chromatic visual testing configuration
import type { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  stories: ['../src/**/*.stories.@(js|jsx|ts|tsx|mdx)'],
  addons: [
    '@storybook/addon-essentials',
    '@storybook/addon-a11y',
    '@storybook/addon-design-tokens',
    '@chromatic-com/storybook',
  ],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  features: {
    buildStoriesJson: true,
  },
  typescript: {
    reactDocgen: 'react-docgen-typescript',
  },
};

export default config;

// Chromatic configuration
module.exports = {
  projectToken: process.env.CHROMATIC_PROJECT_TOKEN,
  buildScriptName: 'build-storybook',
  exitZeroOnChanges: true,
  exitOnceUploaded: true,
  ignoreLastBuildOnBranch: 'main',
  skip: process.env.CI === 'true' && process.env.GITHUB_EVENT_NAME === 'pull_request',
};
```

## Migration and Adoption Strategy
### Gradual Migration Plan
```markdown
# Design System Migration Roadmap

## Phase 1: Foundation (Weeks 1-2)
- Set up design system repository and build pipeline
- Create core design tokens (colors, typography, spacing)
- Implement basic component library (Button, Input, Text)
- Set up Storybook documentation
- Create NPM package distribution

## Phase 2: Core Components (Weeks 3-6)
- Implement essential components (Card, Modal, Form, Navigation)
- Add theme support and dark mode
- Create component testing framework
- Set up visual regression testing
- Begin pilot integration with one microfrontend

## Phase 3: Advanced Components (Weeks 7-10)
- Implement complex components (DataTable, Charts, Calendar)
- Add accessibility testing and compliance
- Create component composition patterns
- Implement Module Federation distribution
- Expand to 2-3 microfrontends

## Phase 4: Full Adoption (Weeks 11-16)
- Migrate all microfrontends to design system
- Implement advanced theming and customization
- Add performance monitoring and optimization
- Create governance and contribution guidelines
- Establish maintenance and update procedures

## Success Metrics
- Component adoption rate across microfrontends
- Design consistency score (visual similarity)
- Development velocity improvement
- Accessibility compliance rate
- User experience satisfaction scores
```

## Output Requirements
1. **Design System Architecture Document**: Comprehensive integration strategy
2. **Component Library Specification**: Detailed component API and usage guidelines
3. **Token Management System**: Design token architecture and distribution
4. **Theme Configuration**: Multi-theme support and customization
5. **Testing Strategy**: Component and visual regression testing approach
6. **Migration Plan**: Step-by-step adoption roadmap

## Quality Checklist
- [ ] Design system architecture supports microfrontend independence
- [ ] Component library provides comprehensive UI coverage
- [ ] Design tokens are well-structured and maintainable
- [ ] Theme system supports customization and branding
- [ ] Testing strategy ensures quality and consistency
- [ ] Migration plan is realistic and low-risk
- [ ] Documentation is comprehensive and accessible
- [ ] Accessibility standards are met (WCAG 2.1 AA)

## Success Criteria
The design system integration is successful when:
1. **Consistency**: Unified user experience across all microfrontends
2. **Efficiency**: Reduced design and development time
3. **Maintainability**: Centralized design system with clear governance
4. **Scalability**: System supports growth and new requirements
5. **Accessibility**: Full compliance with accessibility standards
6. **Performance**: Minimal impact on application performance

## Notes
- Involve designers and developers in the design system creation process
- Establish clear governance and contribution guidelines
- Regularly gather feedback from teams using the design system
- Monitor adoption metrics and iterate based on usage patterns

==================== END: design-system-integration-strategy ====================


==================== START: doc-sharding-task ====================
# Doc Sharding Task

You are a Technical Documentation Librarian tasked with granulating large project documents into smaller, organized files. Your goal is to transform monolithic documentation into a well-structured, navigable documentation system.

## Your Task

Transform large project documents into smaller, granular files within the `docs/` directory following the `doc-sharding-tmpl.txt` plan. Create and maintain `docs/index.md` as a central catalog for easier reference and context injection.

## Execution Process

1. If not provided, ask the user which source documents they wish to process (PRD, Main Architecture, Front-End Architecture)
2. Validate prerequisites:

   - Provided `doc-sharding-tmpl.txt` or access to `bmad-agent/doc-sharding-tmpl.txt`
   - Location of source documents to process
   - Write access to the `docs/` directory
   - Output method (file system or chat interface)

3. For each selected document:

   - Follow the structure in `doc-sharding-tmpl.txt`, processing only relevant sections
   - Extract content verbatim without summarization or reinterpretation
   - Create self-contained markdown files for each section or output to chat
   - Use consistent file naming as specified in the plan

4. For `docs/index.md` when working with the file system:

   - Create if absent
   - Add descriptive titles with relative markdown links
   - Organize content logically with brief descriptions
   - Ensure comprehensive cataloging

5. Maintain creation log and provide final report

## Rules

1. Never modify source content during extraction
2. Create files exactly as specified in the sharding plan
3. Seek approval when consolidating content from multiple sources
4. Maintain original context and meaning
5. Keep file names consistent with the plan
6. Update `index.md` for every new file

## Required Input

1. **Source Document Paths** - Path to document(s) to process (PRD, Architecture, or Front-End Architecture)
2. **Documents to Process** - Which documents to shard in this session
3. **Sharding Plan** - Confirm `docs/templates/doc-sharding-tmpl.txt` exists or `doc-sharding-tmpl.txt` has been provided
4. **Output Location** - Confirm Target directory (default: `docs/`) and index.md or in memory chat output

Would you like to proceed with document sharding? Please provide the required input.

==================== END: doc-sharding-task ====================


==================== START: frontend-service-communication-design ====================
# Frontend Service Communication Design Task

## Objective
Design comprehensive communication patterns and integration strategies for microfrontends to interact with backend services, other microfrontends, and shared infrastructure components.

## Context
You are designing the communication architecture for a distributed frontend system where multiple microfrontends need to coordinate, share data, and integrate with various backend services while maintaining loose coupling and high performance.

## Prerequisites
- Review microfrontend decomposition analysis
- Understand backend service architecture
- Identify data flow requirements between components
- Assess performance and security requirements
- Review existing API contracts and integration patterns

## Task Instructions

### 1. Communication Pattern Analysis

#### Inter-Microfrontend Communication
Design patterns for microfrontend-to-microfrontend communication:

```markdown
# Inter-Microfrontend Communication Patterns

## Event-Driven Communication
### Custom Events Pattern
**Use Cases**:
- User action notifications (login, logout, navigation)
- Data change notifications
- UI state synchronization

**Implementation**:
```typescript
// Event definitions
interface MicrofrontendEvents {
  'user.authenticated': { userId: string; token: string };
  'navigation.changed': { route: string; params: Record<string, any> };
  'data.updated': { entity: string; id: string; data: any };
  'theme.changed': { theme: 'light' | 'dark' };
}

// Event bus implementation
class EventBus {
  private listeners: Map<string, Function[]> = new Map();
  
  publish<K extends keyof MicrofrontendEvents>(
    event: K, 
    data: MicrofrontendEvents[K]
  ): void {
    const eventListeners = this.listeners.get(event) || [];
    eventListeners.forEach(listener => listener(data));
  }
  
  subscribe<K extends keyof MicrofrontendEvents>(
    event: K, 
    handler: (data: MicrofrontendEvents[K]) => void
  ): () => void {
    const eventListeners = this.listeners.get(event) || [];
    eventListeners.push(handler);
    this.listeners.set(event, eventListeners);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(event) || [];
      const index = listeners.indexOf(handler);
      if (index > -1) listeners.splice(index, 1);
    };
  }
}
```

### Shared State Management
**Use Cases**:
- Global application state (user session, preferences)
- Cross-microfrontend data sharing
- Real-time data synchronization

**Implementation**:
```typescript
// Zustand-based shared store
interface GlobalStore {
  user: User | null;
  theme: 'light' | 'dark';
  notifications: Notification[];
  setUser: (user: User | null) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  addNotification: (notification: Notification) => void;
}

const useGlobalStore = create<GlobalStore>((set) => ({
  user: null,
  theme: 'light',
  notifications: [],
  setUser: (user) => set({ user }),
  setTheme: (theme) => set({ theme }),
  addNotification: (notification) => 
    set((state) => ({ 
      notifications: [...state.notifications, notification] 
    })),
}));
```

### Direct Component Integration
**Use Cases**:
- Parent-child component relationships
- Shared component libraries
- Tightly coupled UI components

**Implementation**:
```typescript
// Component props interface
interface MicrofrontendComponentProps {
  data?: any;
  onAction?: (action: string, payload: any) => void;
  config?: ComponentConfig;
  theme?: ThemeConfig;
}

// Microfrontend wrapper component
const MicrofrontendWrapper: React.FC<{
  microfrontend: string;
  props?: MicrofrontendComponentProps;
}> = ({ microfrontend, props }) => {
  const Component = useMicrofrontendComponent(microfrontend);
  
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingSpinner />}>
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};
```

## Backend Service Integration
### API Gateway Pattern
**Architecture**:
```
Microfrontends → API Gateway → Backend Services
```

**Benefits**:
- Centralized authentication and authorization
- Request/response transformation
- Rate limiting and caching
- Service discovery and load balancing

**Implementation**:
```typescript
// API client configuration
interface APIClientConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  headers: Record<string, string>;
}

class APIClient {
  private config: APIClientConfig;
  private interceptors: {
    request: Function[];
    response: Function[];
  } = { request: [], response: [] };
  
  constructor(config: APIClientConfig) {
    this.config = config;
  }
  
  async request<T>(
    method: string,
    url: string,
    data?: any,
    options?: RequestOptions
  ): Promise<T> {
    // Apply request interceptors
    let requestConfig = { method, url, data, ...options };
    for (const interceptor of this.interceptors.request) {
      requestConfig = await interceptor(requestConfig);
    }
    
    // Make request with retry logic
    let lastError: Error;
    for (let attempt = 0; attempt <= this.config.retries; attempt++) {
      try {
        const response = await fetch(requestConfig.url, {
          method: requestConfig.method,
          headers: {
            'Content-Type': 'application/json',
            ...this.config.headers,
            ...requestConfig.headers,
          },
          body: requestConfig.data ? JSON.stringify(requestConfig.data) : undefined,
          signal: AbortSignal.timeout(this.config.timeout),
        });
        
        if (!response.ok) {
          throw new APIError(response.status, await response.text());
        }
        
        let result = await response.json();
        
        // Apply response interceptors
        for (const interceptor of this.interceptors.response) {
          result = await interceptor(result);
        }
        
        return result;
      } catch (error) {
        lastError = error as Error;
        if (attempt < this.config.retries) {
          await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
        }
      }
    }
    
    throw lastError!;
  }
  
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### Backend for Frontend (BFF) Pattern
**Use Cases**:
- Microfrontend-specific data aggregation
- Protocol translation (REST to GraphQL)
- Response optimization for frontend needs

**Implementation**:
```typescript
// BFF service interface
interface UserBFFService {
  getUserDashboard(userId: string): Promise<UserDashboard>;
  getUserProfile(userId: string): Promise<UserProfile>;
  updateUserPreferences(userId: string, preferences: UserPreferences): Promise<void>;
}

// BFF implementation
class UserBFFServiceImpl implements UserBFFService {
  constructor(
    private userService: UserService,
    private orderService: OrderService,
    private notificationService: NotificationService
  ) {}
  
  async getUserDashboard(userId: string): Promise<UserDashboard> {
    // Aggregate data from multiple services
    const [user, recentOrders, notifications] = await Promise.all([
      this.userService.getUser(userId),
      this.orderService.getRecentOrders(userId, 5),
      this.notificationService.getUnreadNotifications(userId)
    ]);
    
    return {
      user: {
        id: user.id,
        name: user.name,
        avatar: user.avatar,
      },
      recentOrders: recentOrders.map(order => ({
        id: order.id,
        status: order.status,
        total: order.total,
        date: order.createdAt,
      })),
      notifications: notifications.map(notification => ({
        id: notification.id,
        message: notification.message,
        type: notification.type,
        timestamp: notification.createdAt,
      })),
    };
  }
}
```

## Real-Time Communication
### WebSocket Integration
**Use Cases**:
- Live notifications
- Real-time data updates
- Collaborative features

**Implementation**:
```typescript
// WebSocket manager
class WebSocketManager {
  private connections: Map<string, WebSocket> = new Map();
  private eventBus: EventBus;
  
  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }
  
  connect(endpoint: string, protocols?: string[]): Promise<WebSocket> {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(endpoint, protocols);
      
      ws.onopen = () => {
        this.connections.set(endpoint, ws);
        resolve(ws);
      };
      
      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.eventBus.publish(message.type, message.payload);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };
      
      ws.onerror = (error) => {
        reject(error);
      };
      
      ws.onclose = () => {
        this.connections.delete(endpoint);
        // Implement reconnection logic
        setTimeout(() => this.connect(endpoint, protocols), 5000);
      };
    });
  }
  
  send(endpoint: string, message: any): void {
    const ws = this.connections.get(endpoint);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }
}
```

### Server-Sent Events (SSE)
**Use Cases**:
- Unidirectional real-time updates
- Live feeds and notifications
- Progress tracking

**Implementation**:
```typescript
// SSE client
class SSEClient {
  private eventSource: EventSource | null = null;
  private eventBus: EventBus;
  
  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }
  
  connect(url: string): void {
    this.eventSource = new EventSource(url);
    
    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.eventBus.publish(data.type, data.payload);
      } catch (error) {
        console.error('Failed to parse SSE message:', error);
      }
    };
    
    this.eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      // Implement reconnection logic
    };
  }
  
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}
```

## Error Handling and Resilience
### Circuit Breaker Pattern
```typescript
// Circuit breaker implementation
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private threshold: number = 5,
    private timeout: number = 60000,
    private monitoringPeriod: number = 10000
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime >= this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}
```

### Retry and Fallback Strategies
```typescript
// Retry with exponential backoff
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError!;
}

// Fallback data provider
class FallbackDataProvider {
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private cacheTTL = 5 * 60 * 1000; // 5 minutes
  
  async getWithFallback<T>(
    key: string,
    primaryProvider: () => Promise<T>,
    fallbackProvider?: () => Promise<T>
  ): Promise<T> {
    try {
      const data = await primaryProvider();
      this.cache.set(key, { data, timestamp: Date.now() });
      return data;
    } catch (error) {
      // Try fallback provider
      if (fallbackProvider) {
        try {
          return await fallbackProvider();
        } catch (fallbackError) {
          // Fall back to cache
          return this.getCachedData(key);
        }
      }
      
      // Fall back to cache
      return this.getCachedData(key);
    }
  }
  
  private getCachedData<T>(key: string): T {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached.data;
    }
    throw new Error(`No fallback data available for ${key}`);
  }
}
```

## Performance Optimization
### Request Batching
```typescript
// Request batcher
class RequestBatcher {
  private batches: Map<string, {
    requests: Array<{ resolve: Function; reject: Function; params: any }>;
    timer: NodeJS.Timeout;
  }> = new Map();
  
  private batchDelay = 50; // 50ms
  
  async batchRequest<T>(
    batchKey: string,
    params: any,
    batchExecutor: (requests: any[]) => Promise<T[]>
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      let batch = this.batches.get(batchKey);
      
      if (!batch) {
        batch = {
          requests: [],
          timer: setTimeout(() => this.executeBatch(batchKey, batchExecutor), this.batchDelay)
        };
        this.batches.set(batchKey, batch);
      }
      
      batch.requests.push({ resolve, reject, params });
    });
  }
  
  private async executeBatch<T>(
    batchKey: string,
    batchExecutor: (requests: any[]) => Promise<T[]>
  ): Promise<void> {
    const batch = this.batches.get(batchKey);
    if (!batch) return;
    
    this.batches.delete(batchKey);
    
    try {
      const params = batch.requests.map(req => req.params);
      const results = await batchExecutor(params);
      
      batch.requests.forEach((req, index) => {
        req.resolve(results[index]);
      });
    } catch (error) {
      batch.requests.forEach(req => {
        req.reject(error);
      });
    }
  }
}
```

### Caching Strategy
```typescript
// Multi-layer cache
class CacheManager {
  private memoryCache: Map<string, { data: any; expiry: number }> = new Map();
  private localStorage: Storage;
  
  constructor() {
    this.localStorage = window.localStorage;
  }
  
  async get<T>(key: string): Promise<T | null> {
    // Check memory cache first
    const memoryData = this.memoryCache.get(key);
    if (memoryData && Date.now() < memoryData.expiry) {
      return memoryData.data;
    }
    
    // Check localStorage
    try {
      const localData = this.localStorage.getItem(key);
      if (localData) {
        const parsed = JSON.parse(localData);
        if (Date.now() < parsed.expiry) {
          // Promote to memory cache
          this.memoryCache.set(key, parsed);
          return parsed.data;
        }
      }
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
    }
    
    return null;
  }
  
  set<T>(key: string, data: T, ttl: number = 300000): void { // 5 minutes default
    const expiry = Date.now() + ttl;
    const cacheEntry = { data, expiry };
    
    // Set in memory cache
    this.memoryCache.set(key, cacheEntry);
    
    // Set in localStorage
    try {
      this.localStorage.setItem(key, JSON.stringify(cacheEntry));
    } catch (error) {
      console.warn('Failed to write to localStorage:', error);
    }
  }
  
  invalidate(key: string): void {
    this.memoryCache.delete(key);
    try {
      this.localStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error);
    }
  }
}
```

## Security Considerations
### Token Management
```typescript
// Secure token manager
class TokenManager {
  private tokenKey = 'auth_token';
  private refreshTokenKey = 'refresh_token';
  
  setTokens(accessToken: string, refreshToken: string): void {
    // Store in httpOnly cookie for security
    document.cookie = `${this.tokenKey}=${accessToken}; Secure; SameSite=Strict; Path=/`;
    document.cookie = `${this.refreshTokenKey}=${refreshToken}; Secure; SameSite=Strict; Path=/`;
  }
  
  getAccessToken(): string | null {
    return this.getCookie(this.tokenKey);
  }
  
  async refreshAccessToken(): Promise<string> {
    const refreshToken = this.getCookie(this.refreshTokenKey);
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }
    
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to refresh token');
    }
    
    const { accessToken, refreshToken: newRefreshToken } = await response.json();
    this.setTokens(accessToken, newRefreshToken);
    
    return accessToken;
  }
  
  private getCookie(name: string): string | null {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  }
}
```

## Output Requirements
1. **Communication Architecture Document**: Comprehensive communication patterns and strategies
2. **API Integration Specifications**: Detailed API client implementations and patterns
3. **Event Schema Definitions**: Type-safe event definitions for inter-microfrontend communication
4. **Error Handling Strategy**: Resilience patterns and fallback mechanisms
5. **Performance Optimization Plan**: Caching, batching, and optimization strategies
6. **Security Implementation**: Token management and secure communication patterns

## Quality Checklist
- [ ] Communication patterns are well-defined and documented
- [ ] Error handling and resilience strategies are comprehensive
- [ ] Performance optimization techniques are implemented
- [ ] Security considerations are addressed
- [ ] Type safety is maintained across all communication interfaces
- [ ] Monitoring and observability are integrated
- [ ] Fallback and degradation strategies are defined

## Success Criteria
The communication design is successful when:
1. **Loose Coupling**: Microfrontends can communicate without tight dependencies
2. **Performance**: Communication is optimized for speed and efficiency
3. **Reliability**: Robust error handling and fallback mechanisms
4. **Security**: Secure communication channels and token management
5. **Scalability**: Communication patterns scale with system growth
6. **Maintainability**: Clear interfaces and well-documented patterns

## Notes
- Consider implementing communication patterns incrementally
- Monitor performance impact of communication overhead
- Regularly review and optimize communication patterns
- Ensure all team members understand the communication architecture

==================== END: frontend-service-communication-design ====================


==================== START: library-indexing-task ====================
# Library Indexing Task

## Purpose

This task maintains the integrity and completeness of the `docs/index.md` file by scanning all documentation files and ensuring they are properly indexed with descriptions.

## Task Instructions

You are now operating as a Documentation Indexer. Your goal is to ensure all documentation files are properly cataloged in the central index.

### Required Steps

1. First, locate and scan:

   - The `docs/` directory and all subdirectories
   - The existing `docs/index.md` file (create if absent)
   - All markdown (`.md`) and text (`.txt`) files in the documentation structure

2. For the existing `docs/index.md`:

   - Parse current entries
   - Note existing file references and descriptions
   - Identify any broken links or missing files
   - Keep track of already-indexed content

3. For each documentation file found:

   - Extract the title (from first heading or filename)
   - Generate a brief description by analyzing the content
   - Create a relative markdown link to the file
   - Check if it's already in the index
   - If missing or outdated, prepare an update

4. For any missing or non-existent files found in index:

   - Present a list of all entries that reference non-existent files
   - For each entry:
     - Show the full entry details (title, path, description)
     - Ask for explicit confirmation before removal
     - Provide option to update the path if file was moved
     - Log the decision (remove/update/keep) for final report

5. Update `docs/index.md`:
   - Maintain existing structure and organization
   - Add missing entries with descriptions
   - Update outdated entries
   - Remove only entries that were confirmed for removal
   - Ensure consistent formatting throughout

### Index Entry Format

Each entry in `docs/index.md` should follow this format:

```markdown
### [Document Title](relative/path/to/file.md)

Brief description of the document's purpose and contents.
```

### Rules of Operation

1. NEVER modify the content of indexed files
2. Preserve existing descriptions in index.md when they are adequate
3. Maintain any existing categorization or grouping in the index
4. Use relative paths for all links
5. Ensure descriptions are concise but informative
6. NEVER remove entries without explicit confirmation
7. Report any broken links or inconsistencies found
8. Allow path updates for moved files before considering removal

### Process Output

The task will provide:

1. A summary of changes made to index.md
2. List of newly indexed files
3. List of updated entries
4. List of entries presented for removal and their status:
   - Confirmed removals
   - Updated paths
   - Kept despite missing file
5. Any other issues or inconsistencies found

### Handling Missing Files

For each file referenced in the index but not found in the filesystem:

1. Present the entry:

   ```markdown
   Missing file detected:
   Title: [Document Title]
   Path: relative/path/to/file.md
   Description: Existing description

   Options:

   1. Remove this entry
   2. Update the file path
   3. Keep entry (mark as temporarily unavailable)

   Please choose an option (1/2/3):
   ```

2. Wait for user confirmation before taking any action
3. Log the decision for the final report

## Required Input

Please provide:

1. Location of the `docs/` directory
2. Confirmation of write access to `docs/index.md`
3. Any specific categorization preferences
4. Any files or directories to exclude from indexing

Would you like to proceed with library indexing? Please provide the required input above.

==================== END: library-indexing-task ====================


==================== START: microfrontend-decomposition-analysis ====================
# Microfrontend Decomposition Analysis Task

## Objective
Analyze the current application or requirements to identify optimal microfrontend boundaries based on business domains, user journeys, team structure, and technical constraints.

## Context
You are performing a strategic analysis to decompose a monolithic frontend or design a new distributed frontend system using microfrontend architecture. This analysis will inform the overall system design and team organization.

## Prerequisites
- Review business requirements and user stories
- Understand current application architecture (if migrating)
- Identify team structure and organizational boundaries
- Analyze user journey maps and workflows
- Assess technical constraints and dependencies

## Task Instructions

### 1. Business Domain Analysis

#### Domain Identification
Create a comprehensive domain map:

```markdown
# Business Domain Map

## Core Domains
### Domain 1: [Domain Name]
- **Business Capability**: [Primary business function]
- **User Value**: [Value delivered to users]
- **Data Entities**: [Key data objects owned]
- **Business Rules**: [Domain-specific business logic]
- **Stakeholders**: [Business stakeholders involved]

### Domain 2: [Domain Name]
- **Business Capability**: [Primary business function]
- **User Value**: [Value delivered to users]
- **Data Entities**: [Key data objects owned]
- **Business Rules**: [Domain-specific business logic]
- **Stakeholders**: [Business stakeholders involved]

## Supporting Domains
### Shared Services
- Authentication & Authorization
- Notification System
- Audit & Logging
- Configuration Management
- File Management

### Generic Subdomains
- User Profile Management
- Settings & Preferences
- Help & Documentation
- Reporting & Analytics
```

#### Domain Boundary Validation
Apply Domain-Driven Design principles:

1. **Bounded Context Identification**
   - Define clear boundaries where domain models are consistent
   - Identify ubiquitous language within each context
   - Map context relationships and integration points

2. **Business Capability Alignment**
   - Ensure each domain represents a complete business capability
   - Validate that domains can operate independently
   - Confirm domains align with organizational structure

3. **Data Ownership Assessment**
   - Identify which domain owns which data entities
   - Define data sharing patterns and dependencies
   - Establish data consistency requirements

### 2. User Journey Mapping

#### Journey Analysis
Map user workflows across domains:

```markdown
# User Journey Analysis

## Primary User Journeys
### Journey 1: [Journey Name]
**User Goal**: [What the user wants to achieve]
**Steps**:
1. [Step 1] → Domain: [Domain Name]
2. [Step 2] → Domain: [Domain Name]
3. [Step 3] → Domain: [Domain Name]

**Cross-Domain Interactions**:
- [Domain A] → [Domain B]: [Interaction type and data]
- [Domain B] → [Domain C]: [Interaction type and data]

**Critical Path**: [Steps that must complete successfully]
**Fallback Scenarios**: [What happens if steps fail]

### Journey 2: [Journey Name]
[Repeat structure for each major journey]

## Secondary User Journeys
[Document less critical but important user flows]

## Administrative Journeys
[Document admin and operational user flows]
```

#### Journey-Domain Alignment
- Ensure journeys can be completed within domain boundaries
- Minimize cross-domain dependencies for critical paths
- Identify shared UI components needed across journeys

### 3. Team Structure Analysis

#### Team Topology Assessment
Analyze current and desired team structure:

```markdown
# Team Structure Analysis

## Current Team Structure
### Team 1: [Team Name]
- **Size**: [Number of members]
- **Skills**: [Technical skills and expertise]
- **Responsibilities**: [Current responsibilities]
- **Domain Knowledge**: [Business domain expertise]

### Team 2: [Team Name]
[Repeat for each team]

## Proposed Microfrontend Teams
### Team Alpha: [Domain Name] Team
- **Microfrontend**: [MF Name]
- **Domain Responsibility**: [Business domain]
- **Team Size**: [Recommended size: 2-8 people]
- **Required Skills**: [Technical skills needed]
- **Autonomy Level**: [High/Medium/Low]

### Team Beta: [Domain Name] Team
[Repeat for each proposed team]

## Shared Platform Team
- **Responsibility**: Design system, shared infrastructure, DevOps
- **Size**: [Number of members]
- **Skills**: [Platform engineering, DevOps, design systems]
```

#### Conway's Law Optimization
- Align microfrontend boundaries with team boundaries
- Ensure teams have full ownership of their microfrontend
- Minimize coordination overhead between teams

### 4. Technical Decomposition Strategy

#### Frontend Component Analysis
Analyze existing or planned UI components:

```markdown
# Component Decomposition Analysis

## Shared Components (Design System)
### Navigation Components
- Global Header
- Main Navigation
- Breadcrumbs
- Footer

### Form Components
- Input Fields
- Buttons
- Validation
- Form Layouts

### Data Display
- Tables
- Cards
- Lists
- Charts

## Domain-Specific Components
### [Domain Name] Components
- [Component 1]: [Description and responsibility]
- [Component 2]: [Description and responsibility]
- [Component 3]: [Description and responsibility]

### [Domain Name] Components
[Repeat for each domain]

## Cross-Cutting Concerns
- Error Handling
- Loading States
- Authentication UI
- Notification System
- Help & Documentation
```

#### Integration Pattern Analysis
Define how microfrontends will integrate:

```markdown
# Integration Pattern Analysis

## Composition Patterns
### Runtime Composition (Module Federation)
**Use Cases**:
- Dynamic loading of microfrontends
- Independent deployment cycles
- Technology diversity

**Implementation**:
- Webpack Module Federation
- Host-Remote pattern
- Shared dependency management

### Build-Time Composition
**Use Cases**:
- Static content and documentation
- Shared component libraries
- Performance-critical paths

**Implementation**:
- NPM package distribution
- Monorepo with shared packages
- Build-time bundling

## Communication Patterns
### Event-Driven Communication
- Custom events for loose coupling
- Event bus for centralized communication
- Type-safe event definitions

### Shared State Management
- Global state for cross-cutting concerns
- Local state for domain-specific data
- State synchronization strategies

### Direct Integration
- Component props for parent-child communication
- Callback functions for user interactions
- Context providers for shared data
```

### 5. Dependency Analysis

#### Technical Dependencies
Map technical dependencies between microfrontends:

```markdown
# Dependency Analysis

## Shared Dependencies
### Core Libraries
- React/Vue/Angular: [Version strategy]
- State Management: [Shared vs. independent]
- Routing: [Global vs. local routing]
- HTTP Client: [Shared configuration]

### Design System
- Component Library: [Centralized distribution]
- Design Tokens: [Token management strategy]
- Styling Framework: [CSS-in-JS vs. utility classes]

### Utility Libraries
- Date/Time: [Moment.js, date-fns, etc.]
- Validation: [Yup, Joi, Zod, etc.]
- Formatting: [Number, currency, etc.]

## Domain Dependencies
### [Domain A] Dependencies
- External APIs: [List of external services]
- Internal Services: [Backend service dependencies]
- Data Sources: [Database, cache, etc.]

### Cross-Domain Dependencies
- [Domain A] → [Domain B]: [Dependency type and reason]
- [Domain B] → [Domain C]: [Dependency type and reason]

## Dependency Management Strategy
- Shared dependency versioning
- Breaking change management
- Dependency update coordination
```

### 6. Migration Strategy (if applicable)

#### Current State Assessment
If migrating from a monolith:

```markdown
# Migration Analysis

## Current Monolith Assessment
### Architecture Overview
- Technology Stack: [Current technologies]
- Component Count: [Number of components]
- Bundle Size: [Current bundle sizes]
- Performance Metrics: [Current performance]

### Code Organization
- Feature Structure: [How features are organized]
- Shared Code: [Shared utilities and components]
- State Management: [Current state management]
- Routing: [Current routing structure]

## Migration Strategy
### Strangler Fig Pattern
**Phase 1**: [First microfrontend to extract]
- Target Domain: [Domain name]
- Extraction Complexity: [High/Medium/Low]
- Business Value: [Value of extracting this domain]
- Timeline: [Estimated timeline]

**Phase 2**: [Second microfrontend to extract]
[Repeat for each phase]

### Risk Assessment
- Technical Risks: [Potential technical challenges]
- Business Risks: [Impact on users and business]
- Mitigation Strategies: [How to address risks]
```

### 7. Decomposition Recommendations

#### Proposed Microfrontend Architecture
Create the final decomposition recommendation:

```markdown
# Recommended Microfrontend Architecture

## Shell Application
**Responsibility**: 
- Global navigation and layout
- Authentication orchestration
- Microfrontend loading and orchestration
- Error boundary management
- Performance monitoring

**Technology**: [Next.js, React, etc.]
**Team**: [Platform/Shell team]

## Microfrontend 1: [Name]
**Domain**: [Business domain]
**Responsibility**: [Key responsibilities]
**User Journeys**: [Primary user journeys served]
**Technology**: [Recommended tech stack]
**Team**: [Responsible team]
**Integration**: [How it integrates with shell and other MFs]

## Microfrontend 2: [Name]
[Repeat structure for each microfrontend]

## Shared Services
### Design System
- Component library distribution
- Design token management
- Theme and branding consistency

### Platform Services
- Authentication service
- Logging and monitoring
- Configuration management
- Error tracking
```

#### Implementation Roadmap
```markdown
# Implementation Roadmap

## Phase 1: Foundation (Weeks 1-4)
- Set up shell application
- Implement design system
- Establish CI/CD pipeline
- Create development environment

## Phase 2: Core Microfrontends (Weeks 5-12)
- Implement [Microfrontend 1]
- Implement [Microfrontend 2]
- Establish integration patterns
- Set up monitoring and logging

## Phase 3: Advanced Features (Weeks 13-20)
- Implement remaining microfrontends
- Optimize performance
- Enhance monitoring and observability
- User acceptance testing

## Phase 4: Production Deployment (Weeks 21-24)
- Production deployment
- Performance optimization
- Security hardening
- Documentation and training
```

## Output Requirements

1. **Domain Boundary Document**: Clear definition of business domains and their boundaries
2. **User Journey Maps**: Detailed user journey analysis with domain interactions
3. **Team Topology Recommendation**: Proposed team structure aligned with microfrontends
4. **Technical Architecture**: High-level technical architecture with integration patterns
5. **Migration Plan**: Step-by-step migration strategy (if applicable)
6. **Implementation Roadmap**: Detailed timeline with milestones and deliverables

## Quality Checklist

- [ ] Business domains are clearly defined and bounded
- [ ] User journeys are mapped to domain boundaries
- [ ] Team structure aligns with microfrontend boundaries
- [ ] Technical dependencies are identified and managed
- [ ] Integration patterns are well-defined
- [ ] Migration strategy is realistic and low-risk
- [ ] Implementation roadmap is detailed and achievable
- [ ] Performance and scalability considerations are addressed
- [ ] Security and compliance requirements are considered

## Success Criteria

The decomposition analysis is successful when:
1. **Clear Boundaries**: Each microfrontend has well-defined responsibilities
2. **Team Alignment**: Microfrontend boundaries align with team capabilities
3. **User Experience**: User journeys are optimally supported
4. **Technical Feasibility**: Implementation is technically achievable
5. **Business Value**: Decomposition delivers clear business benefits
6. **Risk Management**: Risks are identified and mitigation strategies defined

## Notes

- Consider starting with a pilot microfrontend to validate the approach
- Ensure stakeholder buy-in for the proposed decomposition
- Plan for iterative refinement based on implementation learnings
- Document decision rationale for future reference

==================== END: microfrontend-decomposition-analysis ====================


==================== START: microfrontend-deployment-strategy ====================
# Microfrontend Deployment Strategy Task

## Objective
Design and implement a comprehensive deployment strategy for microfrontends that enables independent deployments, zero-downtime updates, and reliable rollback capabilities while maintaining system integrity and performance.

## Context
You are creating a deployment strategy for a distributed frontend system where multiple teams need to deploy their microfrontends independently without affecting other parts of the system. The strategy must support various deployment patterns and ensure production reliability.

## Prerequisites
- Review microfrontend architecture and decomposition
- Understand team structure and deployment requirements
- Assess infrastructure capabilities and constraints
- Identify performance and availability requirements
- Review security and compliance requirements

## Task Instructions

### 1. Deployment Pattern Selection

#### Independent Deployment Strategy
Design autonomous deployment capabilities:

```markdown
# Independent Deployment Architecture

## Core Principles
- **Autonomous Teams**: Each team deploys independently
- **Isolated Failures**: Deployment failures don't affect other microfrontends
- **Version Independence**: Microfrontends can run different versions simultaneously
- **Backward Compatibility**: Maintain compatibility during transitions

## Deployment Isolation
### Container-Based Isolation
```yaml
# Microfrontend deployment manifest
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-microfrontend
  labels:
    app: user-microfrontend
    team: user-team
    version: v1.2.3
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: user-microfrontend
  template:
    metadata:
      labels:
        app: user-microfrontend
        version: v1.2.3
    spec:
      containers:
      - name: user-microfrontend
        image: registry.company.com/user-microfrontend:v1.2.3
        ports:
        - containerPort: 80
        env:
        - name: NODE_ENV
          value: "production"
        - name: API_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: user-mf-config
              key: api-base-url
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Module Federation Deployment
```javascript
// Webpack Module Federation configuration
const ModuleFederationPlugin = require('@module-federation/webpack');

module.exports = {
  mode: 'production',
  plugins: [
    new ModuleFederationPlugin({
      name: 'userMicrofrontend',
      filename: 'remoteEntry.js',
      exposes: {
        './UserProfile': './src/components/UserProfile',
        './UserSettings': './src/components/UserSettings',
      },
      shared: {
        react: { 
          singleton: true, 
          requiredVersion: '^18.0.0',
          eager: false 
        },
        'react-dom': { 
          singleton: true, 
          requiredVersion: '^18.0.0',
          eager: false 
        },
        '@company/design-system': {
          singleton: true,
          requiredVersion: '^2.1.0',
          eager: false
        }
      },
    }),
  ],
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },
};
```

## CI/CD Pipeline Design
### Multi-Stage Pipeline
```yaml
# GitHub Actions workflow for microfrontend
name: Microfrontend CI/CD
on:
  push:
    branches: [main, develop]
    paths: ['microfrontends/user/**']
  pull_request:
    branches: [main]
    paths: ['microfrontends/user/**']

env:
  MICROFRONTEND_NAME: user-microfrontend
  REGISTRY: registry.company.com

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      microfrontend: ${{ steps.changes.outputs.microfrontend }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            microfrontend:
              - 'microfrontends/user/**'

  test:
    needs: changes
    if: needs.changes.outputs.microfrontend == 'true'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: microfrontends/user
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18.19.0'
          cache: 'npm'
          cache-dependency-path: microfrontends/user/package-lock.json
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
      
      - name: Upload test results
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: microfrontends/user/test-results/

  build:
    needs: [changes, test]
    if: needs.changes.outputs.microfrontend == 'true'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: microfrontends/user
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Log in to registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.MICROFRONTEND_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
      
      - name: Build and push
        id: build
        uses: docker/build-push-action@v5
        with:
          context: microfrontends/user
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-staging:
    needs: [changes, build]
    if: needs.changes.outputs.microfrontend == 'true' && github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Deploy to staging
        run: |
          kubectl set image deployment/${{ env.MICROFRONTEND_NAME }} \
            ${{ env.MICROFRONTEND_NAME }}=${{ needs.build.outputs.image-tag }}
          kubectl rollout status deployment/${{ env.MICROFRONTEND_NAME }} --timeout=300s

  deploy-production:
    needs: [changes, build]
    if: needs.changes.outputs.microfrontend == 'true' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Deploy to production
        run: |
          kubectl set image deployment/${{ env.MICROFRONTEND_NAME }} \
            ${{ env.MICROFRONTEND_NAME }}=${{ needs.build.outputs.image-tag }}
          kubectl rollout status deployment/${{ env.MICROFRONTEND_NAME }} --timeout=300s
      
      - name: Validate deployment
        run: |
          # Health check validation
          curl -f https://app.company.com/api/health/${{ env.MICROFRONTEND_NAME }}
          
          # Performance validation
          lighthouse --chrome-flags="--headless" \
            --output=json \
            --output-path=./lighthouse-report.json \
            https://app.company.com/user
          
          # Extract Core Web Vitals
          LCP=$(jq '.audits["largest-contentful-paint"].numericValue' lighthouse-report.json)
          if (( $(echo "$LCP > 2500" | bc -l) )); then
            echo "LCP threshold exceeded: $LCP ms"
            exit 1
          fi
```

## Deployment Strategies
### Blue-Green Deployment
```bash
#!/bin/bash
# Blue-Green deployment script

MICROFRONTEND_NAME="user-microfrontend"
NEW_VERSION="$1"
NAMESPACE="production"

# Validate input
if [ -z "$NEW_VERSION" ]; then
  echo "Usage: $0 <new-version>"
  exit 1
fi

# Determine current and target environments
CURRENT_ENV=$(kubectl get service $MICROFRONTEND_NAME -o jsonpath='{.spec.selector.environment}')
if [ "$CURRENT_ENV" = "blue" ]; then
  TARGET_ENV="green"
else
  TARGET_ENV="blue"
fi

echo "Current environment: $CURRENT_ENV"
echo "Target environment: $TARGET_ENV"
echo "Deploying version: $NEW_VERSION"

# Deploy to target environment
kubectl set image deployment/$MICROFRONTEND_NAME-$TARGET_ENV \
  $MICROFRONTEND_NAME=registry.company.com/$MICROFRONTEND_NAME:$NEW_VERSION

# Wait for deployment to be ready
kubectl rollout status deployment/$MICROFRONTEND_NAME-$TARGET_ENV --timeout=300s

# Validate deployment
echo "Validating deployment..."
TARGET_POD=$(kubectl get pods -l app=$MICROFRONTEND_NAME,environment=$TARGET_ENV -o jsonpath='{.items[0].metadata.name}')
kubectl exec $TARGET_POD -- curl -f http://localhost/health

# Switch traffic
echo "Switching traffic to $TARGET_ENV environment..."
kubectl patch service $MICROFRONTEND_NAME -p '{"spec":{"selector":{"environment":"'$TARGET_ENV'"}}}'

# Validate traffic switch
sleep 10
curl -f https://app.company.com/api/health/$MICROFRONTEND_NAME

echo "Blue-Green deployment completed successfully"
echo "Previous environment ($CURRENT_ENV) is available for rollback"
```

### Canary Deployment
```yaml
# Canary deployment with Istio
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: user-microfrontend-canary
spec:
  hosts:
  - app.company.com
  http:
  - match:
    - headers:
        canary:
          exact: "true"
    route:
    - destination:
        host: user-microfrontend
        subset: canary
      weight: 100
  - route:
    - destination:
        host: user-microfrontend
        subset: stable
      weight: 95
    - destination:
        host: user-microfrontend
        subset: canary
      weight: 5
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: user-microfrontend
spec:
  host: user-microfrontend
  subsets:
  - name: stable
    labels:
      version: stable
  - name: canary
    labels:
      version: canary
```

## Rollback Procedures
### Automated Rollback
```bash
#!/bin/bash
# Automated rollback script

MICROFRONTEND_NAME="$1"
ROLLBACK_VERSION="$2"

if [ -z "$MICROFRONTEND_NAME" ] || [ -z "$ROLLBACK_VERSION" ]; then
  echo "Usage: $0 <microfrontend-name> <rollback-version>"
  exit 1
fi

echo "Initiating rollback for $MICROFRONTEND_NAME to version $ROLLBACK_VERSION"

# Rollback deployment
kubectl rollout undo deployment/$MICROFRONTEND_NAME --to-revision=$ROLLBACK_VERSION

# Wait for rollback to complete
kubectl rollout status deployment/$MICROFRONTEND_NAME --timeout=300s

# Validate rollback
HEALTH_CHECK_URL="https://app.company.com/api/health/$MICROFRONTEND_NAME"
for i in {1..10}; do
  if curl -f "$HEALTH_CHECK_URL" > /dev/null 2>&1; then
    echo "Rollback validation successful (attempt $i)"
    break
  else
    echo "Rollback validation failed (attempt $i), retrying in 30s..."
    sleep 30
  fi
  
  if [ $i -eq 10 ]; then
    echo "Rollback validation failed after 10 attempts"
    exit 1
  fi
done

# Notify teams
curl -X POST "$SLACK_WEBHOOK" \
  -H 'Content-type: application/json' \
  --data "{\"text\":\"🔄 Rollback completed for $MICROFRONTEND_NAME to version $ROLLBACK_VERSION\"}"

echo "Rollback completed successfully"
```

## Performance Monitoring
### Deployment Performance Tracking
```typescript
// Deployment performance monitor
interface DeploymentMetrics {
  microfrontend: string;
  version: string;
  deploymentTime: Date;
  healthCheckDuration: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  errorRate: number;
  userSatisfactionScore: number;
}

class DeploymentMonitor {
  private metrics: DeploymentMetrics[] = [];
  
  async trackDeployment(
    microfrontend: string, 
    version: string
  ): Promise<DeploymentMetrics> {
    const startTime = Date.now();
    
    // Wait for health check to pass
    const healthCheckStart = Date.now();
    await this.waitForHealthCheck(microfrontend);
    const healthCheckDuration = Date.now() - healthCheckStart;
    
    // Collect performance metrics
    const performanceMetrics = await this.collectPerformanceMetrics(microfrontend);
    
    const metrics: DeploymentMetrics = {
      microfrontend,
      version,
      deploymentTime: new Date(startTime),
      healthCheckDuration,
      ...performanceMetrics,
    };
    
    this.metrics.push(metrics);
    await this.reportMetrics(metrics);
    
    return metrics;
  }
  
  private async waitForHealthCheck(microfrontend: string): Promise<void> {
    const maxAttempts = 30;
    const interval = 10000; // 10 seconds
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await fetch(`/api/health/${microfrontend}`);
        if (response.ok) return;
      } catch (error) {
        console.log(`Health check attempt ${attempt} failed:`, error);
      }
      
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }
    
    throw new Error(`Health check failed after ${maxAttempts} attempts`);
  }
  
  private async collectPerformanceMetrics(microfrontend: string) {
    // Use Lighthouse or similar tool to collect metrics
    const lighthouse = await import('lighthouse');
    const chromeLauncher = await import('chrome-launcher');
    
    const chrome = await chromeLauncher.launch({chromeFlags: ['--headless']});
    const options = {logLevel: 'info', output: 'json', port: chrome.port};
    const runnerResult = await lighthouse(`https://app.company.com/${microfrontend}`, options);
    
    await chrome.kill();
    
    const audits = runnerResult.lhr.audits;
    
    return {
      firstContentfulPaint: audits['first-contentful-paint'].numericValue,
      largestContentfulPaint: audits['largest-contentful-paint'].numericValue,
      cumulativeLayoutShift: audits['cumulative-layout-shift'].numericValue,
      errorRate: await this.getErrorRate(microfrontend),
      userSatisfactionScore: await this.getUserSatisfactionScore(microfrontend),
    };
  }
  
  private async reportMetrics(metrics: DeploymentMetrics): Promise<void> {
    // Send metrics to monitoring system
    await fetch('/api/metrics/deployment', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(metrics),
    });
  }
}
```

## Security and Compliance
### Deployment Security Validation
```yaml
# Security scanning in CI/CD
security-scan:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.MICROFRONTEND_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run OWASP ZAP security scan
      uses: zaproxy/action-full-scan@v0.4.0
      with:
        target: 'https://staging.company.com/${{ env.MICROFRONTEND_NAME }}'
        rules_file_name: '.zap/rules.tsv'
        cmd_options: '-a'
```

## Output Requirements
1. **Deployment Architecture Document**: Comprehensive deployment strategy and patterns
2. **CI/CD Pipeline Configuration**: Complete pipeline setup for each microfrontend
3. **Deployment Scripts**: Automated deployment and rollback scripts
4. **Monitoring Setup**: Performance and health monitoring configuration
5. **Security Validation**: Security scanning and compliance procedures
6. **Disaster Recovery Plan**: Rollback and recovery procedures

## Quality Checklist
- [ ] Independent deployment capability for each microfrontend
- [ ] Zero-downtime deployment strategy implemented
- [ ] Automated rollback procedures defined and tested
- [ ] Performance monitoring and validation integrated
- [ ] Security scanning and compliance validation included
- [ ] Disaster recovery procedures documented and tested
- [ ] Team-specific deployment workflows established

## Success Criteria
The deployment strategy is successful when:
1. **Independence**: Teams can deploy without coordination
2. **Reliability**: Zero-downtime deployments with quick rollback
3. **Performance**: Deployment performance meets targets
4. **Security**: All security and compliance requirements met
5. **Observability**: Comprehensive monitoring and alerting
6. **Automation**: Minimal manual intervention required

## Notes
- Start with a pilot microfrontend to validate the deployment strategy
- Ensure all teams are trained on the deployment procedures
- Regularly review and optimize deployment performance
- Maintain comprehensive documentation for troubleshooting

==================== END: microfrontend-deployment-strategy ====================


==================== START: platform-engineering-strategy-design ====================
# Platform Engineering Strategy Design Task
## Internal Developer Platform Architecture and Developer Experience Optimization

## Purpose

- Design comprehensive Internal Developer Platform (IDP) architecture and capabilities
- Optimize developer experience through self-service capabilities and golden paths
- Establish platform-as-a-product approach with clear value propositions
- Create operational excellence framework with SRE practices and automation

Remember as you follow the upcoming instructions:

- Your platform design enables developer productivity and operational excellence
- Output will be used by Platform Teams and Infrastructure Engineers
- Your IDP architecture will determine developer experience and system reliability
- Focus on platform-as-a-product principles and continuous improvement

## Instructions

### 1. Developer Experience Assessment and Requirements

Begin with comprehensive analysis of developer needs and pain points:

#### 1A. Current State Analysis
- **Developer Journey Mapping**: Map current developer workflows from code to production
- **Friction Point Identification**: Identify bottlenecks, manual processes, and pain points
- **Tool Inventory**: Catalog existing development tools and infrastructure
- **Productivity Metrics**: Establish baseline metrics for developer productivity

#### 1B. Developer Persona Analysis
- **Frontend Developers**: Requirements for UI/UX development and deployment
- **Backend Developers**: Requirements for service development and integration
- **Data Engineers**: Requirements for data pipeline and analytics development
- **DevOps Engineers**: Requirements for infrastructure and deployment automation
- **AI/ML Engineers**: Requirements for model development and deployment

#### 1C. Developer Experience Goals
- **Productivity Enhancement**: Reduce time from code to production
- **Cognitive Load Reduction**: Minimize complexity and decision fatigue
- **Self-Service Capabilities**: Enable autonomous team operation
- **Quality and Reliability**: Improve code quality and system reliability

### 2. Internal Developer Platform Architecture

Design comprehensive IDP architecture with core components:

#### 2A. Platform Core Components
- **Developer Portal**: Centralized interface for platform capabilities and documentation
- **Service Catalog**: Self-service catalog of available services and templates
- **CI/CD Platform**: Automated build, test, and deployment pipelines
- **Infrastructure as Code**: Automated infrastructure provisioning and management
- **Configuration Management**: Centralized configuration and secrets management
- **Monitoring and Observability**: Comprehensive system monitoring and alerting

#### 2B. Self-Service Capabilities
- **Service Provisioning**: Automated service creation and deployment
- **Environment Management**: Development, staging, and production environment management
- **Database Provisioning**: Self-service database creation and management
- **Secrets Management**: Secure secrets provisioning and rotation
- **Monitoring Setup**: Automated monitoring and alerting configuration

#### 2C. Golden Path Design
- **Microservice Development**: Opinionated path for microservice development
- **Frontend Development**: Streamlined frontend development and deployment
- **Data Pipeline Development**: Standardized data pipeline creation and management
- **AI/ML Model Deployment**: Simplified machine learning model deployment
- **API Development**: Standardized API development and documentation

### 3. Platform Technology Stack and Infrastructure

Select and design platform technology stack:

#### 3A. Container Orchestration Platform
- **Kubernetes Architecture**: Multi-cluster Kubernetes deployment strategy
- **Cluster Management**: Cluster provisioning, scaling, and lifecycle management
- **Workload Orchestration**: Pod scheduling, resource allocation, and scaling
- **Network Architecture**: Service mesh integration and network policies

#### 3B. CI/CD and GitOps Platform
- **Source Control Integration**: Git workflow and branching strategy integration
- **Build Automation**: Automated build, test, and quality gate execution
- **Deployment Automation**: GitOps-based deployment and rollback capabilities
- **Pipeline as Code**: Version-controlled pipeline definitions and configurations

#### 3C. Observability and Monitoring Stack
- **Metrics Collection**: Prometheus-based metrics collection and storage
- **Logging Platform**: Centralized logging with search and analysis capabilities
- **Distributed Tracing**: End-to-end request tracing across service boundaries
- **Alerting and Notification**: Intelligent alerting and escalation procedures

### 4. Developer Portal and User Experience

Design intuitive developer portal and interfaces:

#### 4A. Portal Architecture
- **Frontend Framework**: Modern web framework for portal development
- **Backend Services**: API services for portal functionality
- **Authentication Integration**: SSO and identity management integration
- **Search and Discovery**: Intelligent search and content discovery

#### 4B. Portal Features and Capabilities
- **Service Catalog Browser**: Interactive service and template catalog
- **Documentation Hub**: Comprehensive documentation with search and navigation
- **Getting Started Guides**: Step-by-step onboarding and tutorial content
- **API Explorer**: Interactive API documentation and testing capabilities
- **Metrics Dashboard**: Developer productivity and service health metrics

#### 4C. User Experience Design
- **Responsive Design**: Mobile-friendly and responsive interface design
- **Accessibility**: WCAG-compliant accessibility features
- **Personalization**: Customizable dashboard and preferences
- **Collaboration Features**: Team collaboration and knowledge sharing

### 5. Operational Excellence and SRE Practices

Implement comprehensive operational excellence framework:

#### 5A. Site Reliability Engineering Implementation
- **Service Level Objectives**: Define SLOs for platform services and capabilities
- **Error Budget Management**: Implement error budget tracking and management
- **Incident Response**: Automated incident detection and response procedures
- **Post-Mortem Process**: Systematic incident analysis and learning

#### 5B. Automation and Self-Healing
- **Infrastructure Automation**: Automated infrastructure provisioning and scaling
- **Self-Healing Systems**: Automated problem detection and remediation
- **Capacity Management**: Automated capacity planning and resource optimization
- **Backup and Recovery**: Automated backup and disaster recovery procedures

#### 5C. Performance and Reliability
- **Performance Monitoring**: Comprehensive performance tracking and optimization
- **Reliability Engineering**: Chaos engineering and resilience testing
- **Scalability Planning**: Automated scaling and capacity management
- **Quality Assurance**: Automated testing and quality gate enforcement

### 6. Security and Compliance Integration

Implement security-by-design and compliance automation:

#### 6A. Security Architecture
- **Identity and Access Management**: Comprehensive IAM and RBAC implementation
- **Secrets Management**: Secure secrets storage, rotation, and distribution
- **Network Security**: Network segmentation and security policy enforcement
- **Container Security**: Container image scanning and runtime protection

#### 6B. Compliance Automation
- **Policy as Code**: Automated policy enforcement and compliance checking
- **Audit Trails**: Comprehensive audit logging and compliance reporting
- **Vulnerability Management**: Automated vulnerability scanning and remediation
- **Regulatory Compliance**: SOX, GDPR, HIPAA, and industry-specific compliance

#### 6C. Security Monitoring
- **Threat Detection**: Automated threat detection and response
- **Security Metrics**: Security posture monitoring and reporting
- **Incident Response**: Security incident response and forensics
- **Penetration Testing**: Regular security testing and assessment

### 7. Platform Team Organization and Governance

Design platform team structure and governance model:

#### 7A. Platform Team Topology
- **Platform Product Manager**: Product strategy and roadmap management
- **Platform Engineers**: Infrastructure and automation development
- **Site Reliability Engineers**: System reliability and operational excellence
- **Developer Experience Engineers**: Developer tooling and experience optimization

#### 7B. Platform Governance
- **Platform Strategy**: Long-term platform vision and strategy
- **Technology Standards**: Technology selection and standardization guidelines
- **Change Management**: Platform change approval and rollout procedures
- **Performance Management**: Platform performance monitoring and optimization

#### 7C. User Engagement and Feedback
- **User Research**: Regular developer needs assessment and feedback collection
- **Community Building**: Developer community engagement and knowledge sharing
- **Training and Support**: Developer training and platform support services
- **Continuous Improvement**: Regular platform capability enhancement and optimization

### 8. Implementation Roadmap and Migration Strategy

Plan phased platform implementation and adoption:

#### 8A. Implementation Phases
- **Phase 1 - Foundation**: Core infrastructure and basic self-service capabilities
- **Phase 2 - Developer Experience**: Developer portal and golden path implementation
- **Phase 3 - Advanced Capabilities**: AI/ML platform and advanced automation
- **Phase 4 - Optimization**: Performance optimization and advanced features

#### 8B. Migration Strategy
- **Legacy System Integration**: Integration with existing systems and tools
- **Gradual Migration**: Phased migration of teams and applications
- **Training and Onboarding**: Developer training and platform adoption programs
- **Change Management**: Organizational change management and communication

#### 8C. Success Metrics and KPIs
- **Developer Productivity**: Deployment frequency, lead time, and recovery time
- **Platform Adoption**: Service onboarding rate and platform usage metrics
- **Operational Excellence**: System reliability, availability, and performance
- **Business Impact**: Cost optimization and business value delivery

## Deliverables

### Primary Deliverable
Comprehensive Platform Engineering Strategy including:
- Internal Developer Platform architecture with technology stack
- Developer experience optimization plan with self-service capabilities
- Operational excellence framework with SRE practices
- Security and compliance integration with automation
- Platform team organization with governance model
- Implementation roadmap with migration strategy
- Success metrics and KPI framework

### Secondary Deliverables
- Platform capability specifications with technical requirements
- Developer portal design with user experience mockups
- Golden path documentation with implementation guides
- Security and compliance framework with policy definitions
- Training and onboarding materials for developers and platform teams

## Success Criteria

- IDP architecture enables developer self-service and productivity
- Developer experience significantly improves with reduced friction
- Operational excellence achieved with automated reliability and scaling
- Security and compliance integrated with automated enforcement
- Platform team organization supports platform-as-a-product approach
- Implementation roadmap provides clear path to platform maturity
- Success metrics demonstrate business value and developer satisfaction

## Validation Checklist

- [ ] IDP architecture comprehensive with all core components defined
- [ ] Developer experience optimization addresses identified pain points
- [ ] Self-service capabilities enable team autonomy and productivity
- [ ] Golden paths provide opinionated but flexible development workflows
- [ ] Operational excellence framework includes SRE practices and automation
- [ ] Security and compliance integrated with policy-as-code approach
- [ ] Platform team organization aligned with platform-as-a-product principles
- [ ] Implementation roadmap realistic with clear phases and success criteria

==================== END: platform-engineering-strategy-design ====================


==================== START: service-decomposition-analysis ====================
# Service Decomposition Analysis Task
## Domain-Driven Service Boundary Identification and Team Topology Planning

## Purpose

- Analyze business capabilities and identify optimal microservice boundaries using domain-driven design
- Apply Conway's Law principles to align service boundaries with organizational structure
- Assess service sizing, complexity, and team topology considerations
- Recommend service catalog and ownership model for enterprise-scale microservices

Remember as you follow the upcoming instructions:

- Your analysis forms the foundation for the entire microservices architecture
- Output will be used by Platform Engineers and Service Mesh Architects
- Your service boundaries will determine team structure and communication patterns
- Focus on business value alignment and organizational feasibility

## Instructions

### 1. Business Capability Analysis

Begin with comprehensive business capability mapping:

#### 1A. Value Stream Identification
- **Customer Journey Mapping**: Map end-to-end customer journeys and touchpoints
- **Business Process Analysis**: Identify core business processes and workflows
- **Value Chain Analysis**: Understand value creation and delivery mechanisms
- **Stakeholder Mapping**: Identify internal and external stakeholders and their interactions

#### 1B. Business Capability Decomposition
- **Core Capabilities**: Identify essential business capabilities that differentiate the organization
- **Supporting Capabilities**: Map supporting functions that enable core capabilities
- **Generic Capabilities**: Identify commodity capabilities that could be outsourced or standardized
- **Capability Dependencies**: Understand relationships and dependencies between capabilities

#### 1C. Data Flow Analysis
- **Information Architecture**: Map information flows between business capabilities
- **Data Ownership**: Identify natural data ownership boundaries and responsibilities
- **Data Consistency Requirements**: Understand consistency and transaction requirements
- **Data Privacy and Security**: Consider data protection and compliance requirements

### 2. Domain Modeling and Bounded Context Identification

Apply domain-driven design principles to identify service boundaries:

#### 2A. Domain Expert Collaboration
- **Domain Knowledge Extraction**: Collaborate with domain experts to understand business rules
- **Ubiquitous Language**: Establish common vocabulary and terminology for each domain
- **Business Rule Identification**: Document business rules and constraints within each domain
- **Domain Event Identification**: Identify significant business events and their triggers

#### 2B. Bounded Context Definition
- **Context Boundaries**: Define clear boundaries where domain models apply
- **Context Relationships**: Understand relationships between bounded contexts
- **Shared Kernel Identification**: Identify shared concepts and models across contexts
- **Anti-Corruption Layers**: Plan protection mechanisms for context boundaries

#### 2C. Aggregate and Entity Modeling
- **Aggregate Root Identification**: Identify aggregate roots and their boundaries
- **Entity Relationships**: Map relationships between entities within aggregates
- **Value Object Definition**: Identify value objects and their usage patterns
- **Domain Service Requirements**: Understand domain services and their responsibilities

### 3. Service Sizing and Complexity Assessment

Evaluate optimal service granularity and complexity:

#### 3A. Service Granularity Analysis
- **Single Responsibility Assessment**: Ensure each service has a single, well-defined responsibility
- **Cohesion Evaluation**: Assess functional cohesion within proposed service boundaries
- **Coupling Analysis**: Minimize coupling between services while maintaining necessary integration
- **Interface Complexity**: Evaluate complexity of service interfaces and contracts

#### 3B. Team Cognitive Load Assessment
- **Team Size Considerations**: Apply two-pizza team rule and team size constraints
- **Skill Requirements**: Assess skill requirements and team capability alignment
- **Cognitive Load Evaluation**: Ensure teams can effectively own and operate their services
- **Domain Expertise Alignment**: Align domain expertise with service ownership

#### 3C. Operational Complexity Evaluation
- **Deployment Complexity**: Assess deployment and operational complexity for each service
- **Monitoring Requirements**: Understand monitoring and observability needs
- **Scaling Patterns**: Evaluate scaling requirements and patterns for each service
- **Technology Stack Considerations**: Consider technology diversity and operational overhead

### 4. Conway's Law Application and Team Topology Planning

Align service boundaries with desired organizational structure:

#### 4A. Current Organizational Analysis
- **Team Structure Assessment**: Analyze current team structure and communication patterns
- **Communication Flow Mapping**: Map current communication flows and collaboration patterns
- **Skill Distribution**: Understand skill distribution across teams and individuals
- **Organizational Constraints**: Identify organizational constraints and change capacity

#### 4B. Target Team Topology Design
- **Stream-Aligned Teams**: Design teams aligned with value streams and customer outcomes
- **Enabling Teams**: Plan enabling teams for platform capabilities and specialist knowledge
- **Complicated Subsystem Teams**: Identify teams for complex technical subsystems
- **Platform Teams**: Design platform teams for shared infrastructure and capabilities

#### 4C. Team Interaction Patterns
- **Collaboration Patterns**: Define collaboration patterns between teams
- **X-as-a-Service Patterns**: Plan service provision patterns between teams
- **Facilitating Patterns**: Design facilitation and knowledge sharing patterns
- **Communication Protocols**: Establish communication protocols and interfaces

### 5. Service Catalog and Ownership Model

Define comprehensive service catalog with clear ownership:

#### 5A. Service Classification
- **Core Business Services**: Services that implement core business capabilities
- **Supporting Services**: Services that support core business functionality
- **Infrastructure Services**: Services that provide technical infrastructure capabilities
- **Integration Services**: Services that handle external system integration

#### 5B. Service Ownership Definition
- **Service Owner Identification**: Assign clear service ownership to teams
- **Responsibility Matrix**: Define responsibilities for development, operation, and evolution
- **Decision Rights**: Establish decision-making authority for service changes
- **Accountability Framework**: Create accountability framework for service outcomes

#### 5C. Service Lifecycle Management
- **Service Evolution Strategy**: Plan service evolution and versioning approaches
- **Deprecation Policies**: Establish service deprecation and retirement policies
- **Dependency Management**: Plan dependency management and change coordination
- **Service Registry**: Design service registry and discovery mechanisms

### 6. Technology and Infrastructure Implications

Consider technology implications of service decomposition:

#### 6A. Technology Stack Assessment
- **Polyglot Persistence**: Evaluate database technology choices for each service
- **Programming Language Selection**: Consider programming language choices and constraints
- **Framework and Library Decisions**: Assess framework choices and standardization needs
- **Technology Diversity Management**: Balance innovation with operational complexity

#### 6B. Infrastructure Requirements
- **Container Orchestration**: Plan Kubernetes deployment and management strategies
- **Service Mesh Integration**: Consider service mesh requirements and implementation
- **API Gateway Strategy**: Plan API gateway and traffic management approaches
- **Monitoring and Observability**: Design monitoring and observability infrastructure

#### 6C. Data Architecture Implications
- **Database per Service**: Plan database isolation and data ownership strategies
- **Data Synchronization**: Design data synchronization and consistency mechanisms
- **Event-Driven Architecture**: Plan event streaming and message broker requirements
- **Data Pipeline Architecture**: Consider data pipeline and analytics requirements

### 7. Risk Assessment and Mitigation

Identify and mitigate risks associated with service decomposition:

#### 7A. Technical Risks
- **Distributed System Complexity**: Assess complexity of distributed system management
- **Network Latency and Reliability**: Consider network-related performance risks
- **Data Consistency Challenges**: Evaluate eventual consistency and transaction risks
- **Service Discovery and Configuration**: Assess service discovery and configuration complexity

#### 7B. Organizational Risks
- **Team Coordination Overhead**: Evaluate coordination overhead between teams
- **Skill Gap Risks**: Identify skill gaps and training requirements
- **Change Management Challenges**: Assess organizational change management needs
- **Communication and Collaboration**: Consider communication and collaboration challenges

#### 7C. Business Risks
- **Delivery Timeline Impact**: Assess impact on delivery timelines and milestones
- **Quality and Reliability Risks**: Consider quality and reliability implications
- **Cost and Resource Implications**: Evaluate cost and resource requirements
- **Customer Experience Impact**: Assess potential impact on customer experience

### 8. Implementation Roadmap and Migration Strategy

Plan phased implementation and migration approach:

#### 8A. Migration Strategy
- **Strangler Fig Pattern**: Plan gradual migration using strangler fig approach
- **Database Decomposition**: Plan database decomposition and data migration
- **Service Extraction**: Design service extraction and refactoring strategies
- **Legacy System Integration**: Plan integration with existing legacy systems

#### 8B. Implementation Phases
- **Phase 1 - Foundation**: Establish platform infrastructure and core services
- **Phase 2 - Core Services**: Implement core business services and capabilities
- **Phase 3 - Integration**: Implement service integration and communication patterns
- **Phase 4 - Optimization**: Optimize performance, scaling, and operational excellence

#### 8C. Success Metrics and Validation
- **Business Metrics**: Define business value and outcome metrics
- **Technical Metrics**: Establish technical performance and quality metrics
- **Team Metrics**: Define team productivity and satisfaction metrics
- **Operational Metrics**: Plan operational excellence and reliability metrics

## Deliverables

### Primary Deliverable
Comprehensive Service Decomposition Analysis including:
- Business capability map with service boundary recommendations
- Domain model with bounded contexts and aggregate definitions
- Service catalog with ownership and responsibility assignments
- Team topology recommendations with interaction patterns
- Technology implications and infrastructure requirements
- Risk assessment with mitigation strategies
- Implementation roadmap with migration strategy

### Secondary Deliverables
- Service boundary decision records with rationale
- Team topology design with communication patterns
- Technology stack recommendations with trade-off analysis
- Risk register with mitigation plans
- Implementation timeline with milestones and dependencies

## Success Criteria

- Service boundaries align with business capabilities and domain boundaries
- Team topology supports effective service ownership and operation
- Service sizing enables team autonomy while minimizing coordination overhead
- Technology choices support service independence and operational excellence
- Implementation roadmap provides clear path from current state to target architecture
- Risk mitigation strategies address identified technical and organizational challenges

## Validation Checklist

- [ ] Business capabilities mapped to service boundaries with clear rationale
- [ ] Domain models defined with bounded contexts and aggregate boundaries
- [ ] Service sizing appropriate for team cognitive load and operational complexity
- [ ] Team topology aligned with Conway's Law and organizational constraints
- [ ] Technology implications assessed with infrastructure requirements defined
- [ ] Risk assessment comprehensive with mitigation strategies identified
- [ ] Implementation roadmap realistic with clear phases and milestones
- [ ] Service ownership model clear with accountability framework established

==================== END: service-decomposition-analysis ====================


==================== START: story-draft-task ====================
# Story Draft Task

## Objective
Draft a comprehensive user story for development that follows BMAD methodology standards and includes all necessary technical specifications for implementation.

## Context
You are creating a development-ready story that will be used by development agents to implement specific functionality. The story must be complete, testable, and aligned with the overall project architecture.

## Prerequisites
- Review the Project Requirements Document (PRD)
- Understand the overall system architecture
- Identify the epic this story belongs to
- Ensure alignment with acceptance criteria

## Task Instructions

### 1. Story Structure Creation
Create a story file following this structure:

```markdown
# Story: [Epic].[Story Number] - [Brief Title]

## Story Overview
**As a** [user type]
**I want** [functionality]
**So that** [business value]

## Epic Context
- **Epic**: [Epic Name and Number]
- **Priority**: [High/Medium/Low]
- **Estimated Effort**: [Story Points or Time Estimate]
- **Dependencies**: [List any dependent stories or external dependencies]

## Acceptance Criteria
1. [Specific, testable criterion]
2. [Specific, testable criterion]
3. [Specific, testable criterion]

## Technical Requirements

### Implementation Details
- **Components Affected**: [List of system components]
- **API Endpoints**: [If applicable]
- **Database Changes**: [If applicable]
- **UI/UX Requirements**: [If applicable]

### Technical Constraints
- [Any technical limitations or requirements]
- [Performance requirements]
- [Security considerations]

## Definition of Done
- [ ] All acceptance criteria met
- [ ] Code reviewed and approved
- [ ] Unit tests written and passing
- [ ] Integration tests passing
- [ ] Documentation updated
- [ ] Security review completed (if applicable)
- [ ] Performance requirements met
- [ ] Deployed to staging environment
- [ ] User acceptance testing completed

## Testing Strategy
### Unit Tests
- [Describe unit testing approach]

### Integration Tests
- [Describe integration testing requirements]

### User Acceptance Tests
- [Describe UAT scenarios]

## Implementation Notes
- [Any specific implementation guidance]
- [Architectural considerations]
- [Code patterns to follow]

## Risk Assessment
- **Technical Risks**: [Identify potential technical challenges]
- **Business Risks**: [Identify potential business impact]
- **Mitigation Strategies**: [How to address identified risks]
```

### 2. Story Validation
Ensure the story meets these criteria:
- **Completeness**: All sections are filled with relevant information
- **Clarity**: Requirements are unambiguous and specific
- **Testability**: Acceptance criteria can be objectively verified
- **Feasibility**: Story can be completed within a single sprint
- **Value**: Delivers clear business or user value

### 3. Technical Alignment
Verify the story aligns with:
- Overall system architecture
- Coding standards and patterns
- Security requirements
- Performance requirements
- Integration patterns

### 4. Documentation Requirements
- Save the story in `docs/stories/[epic-number].[story-number].story.md`
- Update the epic tracking document
- Link to relevant architecture documents
- Reference any design specifications

## Output Requirements
1. **Complete Story File**: Properly formatted and saved in the correct location
2. **Epic Update**: Update epic documentation to include this story
3. **Dependency Mapping**: Document any dependencies or blockers
4. **Estimation**: Provide effort estimation for development

## Quality Checklist
- [ ] Story follows BMAD methodology standards
- [ ] All acceptance criteria are specific and testable
- [ ] Technical requirements are complete and clear
- [ ] Definition of Done is comprehensive
- [ ] Story is properly sized for a single sprint
- [ ] Dependencies are identified and documented
- [ ] Risk assessment is complete
- [ ] Story aligns with overall architecture

## Success Criteria
The story is considered complete when:
1. Development team can implement without additional clarification
2. All acceptance criteria can be objectively tested
3. Story delivers measurable business value
4. Technical implementation is clearly defined
5. Quality assurance approach is established

## Notes
- Collaborate with Product Owner for business requirements clarification
- Consult with Architect for technical feasibility
- Engage with Design Architect for UI/UX requirements
- Coordinate with other team members for dependency management

==================== END: story-draft-task ====================


