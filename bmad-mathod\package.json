{"name": "bmad-method", "version": "4.0.0", "description": "BMAD 4.0 - Microservices & AI-Native Methodology Framework", "main": "build-web-agent.js", "scripts": {"build": "node build-web-agent.js", "build:validate": "node build-web-agent.js && npm run validate:build", "validate:build": "node -e \"const fs = require('fs'); const path = './build'; if (!fs.existsSync(path)) throw new Error('Build directory not found'); console.log('Build validation passed');\"", "setup": "npm install && npm run build", "test": "npm run validate:config && npm run build:validate", "validate:config": "node -c build-web-agent.js && node -e \"require('./build-web-agent.cfg.js'); console.log('Configuration valid');\"", "clean": "node -e \"const fs = require('fs'); const path = './build'; if (fs.existsSync(path)) fs.rmSync(path, {recursive: true}); console.log('Build directory cleaned');\"", "dev": "npm run clean && npm run build", "deploy:web": "npm run build && echo 'Web agent bundle ready for deployment in ./build/'", "validate:agents": "node scripts/validate-agents.js", "lint": "echo 'Linting configuration - add ESLint when needed'", "format": "echo 'Code formatting - add Prettier when needed'"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["bmad", "methodology", "microservices", "ai-native", "agents", "orchestration", "platform-engineering", "service-mesh", "enterprise-architecture"], "author": "BMAD Method Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bmad-method/bmad-method.git"}, "bugs": {"url": "https://github.com/bmad-method/bmad-method/issues"}, "homepage": "https://github.com/bmad-method/bmad-method#readme", "dependencies": {"dotenv": "^16.0.0"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^3.0.0"}, "files": ["bmad-agent/", "build-web-agent.js", "build-web-agent.cfg.js", "docs/", "scripts/", "README.md"], "bmad": {"version": "4.0", "type": "microservices-ai-native", "agents": {"orchestrator": "web-bmad-orchestrator-agent", "ide": "ide-bmad-orchestrator"}, "build": {"target": "web-agents", "output": "./build/"}}}