A simple project run through the Web Gemini BMad Agent - all artifacts from a single chat session (split up into smaller files with the sharding task)

- The [Project Brief](./v3-output-demo-files/project-brief.md) was first collaborated on and created with the Analyst
- The first [PRD Draft](./v3-output-demo-files/prd.draft.md) was created with the PM
- The [Architecture](./v3-output-demo-files/architecture.md) was created and then we worked on some design artifacts. The architect conversation lead to changes in the PRD reflected later.

Design Artifacts with the Design Architect:

- [UX UI Spec](./v3-output-demo-files/ux-ui-spec.md)
- [V0 1 Shot UI Prompt](./v3-output-demo-files/v0-prompt.md)
- [Front End Architecture](./v3-output-demo-files/front-end-architecture.md)

Then the updated PRD with fixed Expic and Stories after running the PO Checklist. The PO took all changes from the architect and design architect and worked them back into the updated [PRD Final](./v3-output-demo-files/prd.md)
