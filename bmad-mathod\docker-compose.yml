version: '3.8'

services:
  # Development environment
  bmad-dev:
    build:
      context: .
      target: development
    container_name: bmad-method-dev
    volumes:
      - .:/app
      - /app/node_modules
      - bmad-build:/app/build
    environment:
      - NODE_ENV=development
      - BMAD_BUILD_DIR=/app/build
      - BMAD_ASSET_ROOT=/app/bmad-agent
      - BMAD_VALIDATE_REFERENCES=true
    ports:
      - "3000:3000"
    command: npm run dev
    restart: unless-stopped

  # Production build
  bmad-build:
    build:
      context: .
      target: build
    container_name: bmad-method-build
    volumes:
      - bmad-build:/app/build
    environment:
      - NODE_ENV=production
      - BMAD_VERSION=4.0.0
    command: npm run build

  # Production runtime
  bmad-prod:
    build:
      context: .
      target: production
    container_name: bmad-method-prod
    volumes:
      - bmad-build:/app/build:ro
    environment:
      - NODE_ENV=production
      - BMAD_VERSION=4.0.0
    restart: unless-stopped

  # CI/CD testing
  bmad-ci:
    build:
      context: .
      target: ci
    container_name: bmad-method-ci
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=test
      - CI=true
    command: npm test

  # Validation service
  bmad-validate:
    build:
      context: .
      target: development
    container_name: bmad-method-validate
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    command: npm run validate:agents

volumes:
  bmad-build:
    driver: local

networks:
  default:
    name: bmad-network
