# IDE Setup Guide for BMAD Method v4.0

This guide provides comprehensive instructions for setting up BMAD Method v4.0 agents in various IDE environments.

## Overview

The BMAD Method v4.0 supports multiple IDE environments with enhanced agent configurations. This guide covers setup for the most popular IDEs and AI coding assistants.

## Quick Start

1. **Copy the bmad-agent folder** to the root of your project
2. **Choose your IDE setup method** from the options below
3. **Configure custom modes** for your preferred agents
4. **Start with the BMAD Orchestrator** or individual specialized agents

## Cursor IDE Setup

### Prerequisites
- Cursor IDE installed
- Custom modes enabled in Settings → Features → Chat → Custom modes

### Single Agent Setup
1. Create a custom mode following Cursor's documentation
2. Copy content from any `.ide.md` file in `bmad-agent/personas/`
3. Paste into the custom mode configuration

### BMAD Orchestrator Setup
1. Copy the full `bmad-agent` folder to your project root
2. Create a new custom mode in Cursor
3. Copy the content from `bmad-agent/ide-bmad-orchestrator.md`
4. Paste into the custom mode configuration

### Task Execution
- Drag task files from `bmad-agent/tasks/` into any agent chat
- Ask the agent to complete the specific task
- Useful for one-off actions when custom mode limits are reached

## Windsurf IDE Setup

### Agent Configuration
1. Click "Windsurf - Settings" (bottom right)
2. Access Advanced Settings via settings panel or profile dropdown
3. Configure custom AI rules for Cascade

### Custom Rules Configuration
- Define custom AI rules for specific BMAD roles
- Specify response patterns and framework preferences
- Configure API usage patterns

### Flows Implementation
1. Combine Agents and Copilots for comprehensive workflows
2. Use Model Context Protocol (MCP) to extend capabilities
3. Create custom agents for each BMAD role
4. Configure appropriate permissions and capabilities

## RooCode Setup

### Custom Modes Configuration
1. Create tailored AI behaviors through configuration files
2. Set specific prompts, file restrictions, and auto-approval settings
3. Configure different models per mode

### BMAD Method Implementation
1. Create distinct modes for each role:
   - Analyst (Mary) - Brief creation specialist
   - Product Manager (John) - PRD creation specialist
   - Architect (Fred) - System architecture specialist
   - Design Architect (Jane) - UI/UX & microfrontend expert
   - Service Mesh Architect (Alex) - Distributed communication expert
   - Platform Engineer (Taylor) - IDP and developer experience
   - AI Orchestration Specialist (Morgan) - Multi-agent systems
   - Microfrontend Architect (Jordan) - Distributed frontend systems
   - Product Owner (Sarah) - Story and epic management
   - Scrum Master (Bob) - Process and story development
   - BMAD Orchestrator - Central coordination

2. Customize each mode with role-specific prompts
3. Configure file restrictions appropriate to each role
4. Set up direct mode switching capabilities

### Model Configuration
- Configure different models per mode based on complexity
- Supports multiple API providers (OpenRouter, Anthropic, OpenAI, Google Gemini, AWS Bedrock, Azure, local models)
- Monitor token and cost usage for optimization

## Cline Setup

### Custom Instructions
1. Access via Cline > Settings > Custom Instructions
2. Provide behavioral guidelines for BMAD agents
3. Configure role-specific instructions

### Custom Tools Integration
1. Use Model Context Protocol (MCP) for extended capabilities
2. Ask Cline to "add a tool" for specific BMAD workflows
3. Custom tools saved at `~/Documents/Cline/MCP`
4. Create tools for each BMAD role and workflow

### Model Selection
- Configure different models based on role and task complexity
- Optimize for autonomous workflow handling

## GitHub Copilot (Future Support)

### Current Status
GitHub Copilot is developing Copilot Extensions system for custom agents.

### Planned Features
1. **Copilot Extensions**: Custom functionality integration
2. **Custom Agents**: GitHub App integration with Copilot agents
3. **Custom Instructions**: Enhanced behavioral guidance

*Note: Full custom mode configuration is under development. Check GitHub's documentation for updates.*

## Best Practices

### Agent Selection
- Use **BMAD Orchestrator** for agent coordination and selection
- Use **specialized agents** for focused tasks (recommended for frequent use)
- Use **task files** for one-off actions when mode limits are reached

### Workflow Optimization
1. **Planning Phase**: Use Analyst → PM → Architect workflow
2. **Implementation Phase**: Use PO → SM → Dev workflow
3. **Modern Architecture**: Leverage Service Mesh, Platform Engineer, AI Orchestration, and Microfrontend Architects

### Performance Tips
- Dedicated agents use less context overhead than the orchestrator
- Use specialized agents (sm.ide.md, dev.ide.md) for frequent tasks
- Keep the bmad-agent folder at project root for proper path resolution

## Troubleshooting

### Common Issues
1. **Path Resolution**: Ensure bmad-agent folder is at project root
2. **Mode Limits**: Use task files when custom mode limits are reached
3. **Configuration Errors**: Verify all file paths and references

### Support Resources
- Check IDE-specific documentation for custom mode setup
- Review BMAD Method v4.0 knowledge base for workflow guidance
- Use the BMAD Orchestrator's help commands for guidance

## Advanced Configuration

### Multi-IDE Workflow
- Use web-based orchestrator for high-level planning
- Use IDE agents for implementation and development
- Export documents between environments as needed

### Team Collaboration
- Share custom mode configurations across team
- Standardize on BMAD Method v4.0 workflows
- Use consistent agent naming and role assignments

This setup guide ensures you can leverage the full power of BMAD Method v4.0's enhanced capabilities across any supported IDE environment.
