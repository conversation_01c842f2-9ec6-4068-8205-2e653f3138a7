# Service Decomposition Analysis Task
## Domain-Driven Service Boundary Identification and Team Topology Planning

## Purpose

- Analyze business capabilities and identify optimal microservice boundaries using domain-driven design
- Apply <PERSON>'s Law principles to align service boundaries with organizational structure
- Assess service sizing, complexity, and team topology considerations
- Recommend service catalog and ownership model for enterprise-scale microservices

Remember as you follow the upcoming instructions:

- Your analysis forms the foundation for the entire microservices architecture
- Output will be used by Platform Engineers and Service Mesh Architects
- Your service boundaries will determine team structure and communication patterns
- Focus on business value alignment and organizational feasibility

## Instructions

### 1. Business Capability Analysis

Begin with comprehensive business capability mapping:

#### 1A. Value Stream Identification
- **Customer Journey Mapping**: Map end-to-end customer journeys and touchpoints
- **Business Process Analysis**: Identify core business processes and workflows
- **Value Chain Analysis**: Understand value creation and delivery mechanisms
- **Stakeholder Mapping**: Identify internal and external stakeholders and their interactions

#### 1B. Business Capability Decomposition
- **Core Capabilities**: Identify essential business capabilities that differentiate the organization
- **Supporting Capabilities**: Map supporting functions that enable core capabilities
- **Generic Capabilities**: Identify commodity capabilities that could be outsourced or standardized
- **Capability Dependencies**: Understand relationships and dependencies between capabilities

#### 1C. Data Flow Analysis
- **Information Architecture**: Map information flows between business capabilities
- **Data Ownership**: Identify natural data ownership boundaries and responsibilities
- **Data Consistency Requirements**: Understand consistency and transaction requirements
- **Data Privacy and Security**: Consider data protection and compliance requirements

### 2. Domain Modeling and Bounded Context Identification

Apply domain-driven design principles to identify service boundaries:

#### 2A. Domain Expert Collaboration
- **Domain Knowledge Extraction**: Collaborate with domain experts to understand business rules
- **Ubiquitous Language**: Establish common vocabulary and terminology for each domain
- **Business Rule Identification**: Document business rules and constraints within each domain
- **Domain Event Identification**: Identify significant business events and their triggers

#### 2B. Bounded Context Definition
- **Context Boundaries**: Define clear boundaries where domain models apply
- **Context Relationships**: Understand relationships between bounded contexts
- **Shared Kernel Identification**: Identify shared concepts and models across contexts
- **Anti-Corruption Layers**: Plan protection mechanisms for context boundaries

#### 2C. Aggregate and Entity Modeling
- **Aggregate Root Identification**: Identify aggregate roots and their boundaries
- **Entity Relationships**: Map relationships between entities within aggregates
- **Value Object Definition**: Identify value objects and their usage patterns
- **Domain Service Requirements**: Understand domain services and their responsibilities

### 3. Service Sizing and Complexity Assessment

Evaluate optimal service granularity and complexity:

#### 3A. Service Granularity Analysis
- **Single Responsibility Assessment**: Ensure each service has a single, well-defined responsibility
- **Cohesion Evaluation**: Assess functional cohesion within proposed service boundaries
- **Coupling Analysis**: Minimize coupling between services while maintaining necessary integration
- **Interface Complexity**: Evaluate complexity of service interfaces and contracts

#### 3B. Team Cognitive Load Assessment
- **Team Size Considerations**: Apply two-pizza team rule and team size constraints
- **Skill Requirements**: Assess skill requirements and team capability alignment
- **Cognitive Load Evaluation**: Ensure teams can effectively own and operate their services
- **Domain Expertise Alignment**: Align domain expertise with service ownership

#### 3C. Operational Complexity Evaluation
- **Deployment Complexity**: Assess deployment and operational complexity for each service
- **Monitoring Requirements**: Understand monitoring and observability needs
- **Scaling Patterns**: Evaluate scaling requirements and patterns for each service
- **Technology Stack Considerations**: Consider technology diversity and operational overhead

### 4. Conway's Law Application and Team Topology Planning

Align service boundaries with desired organizational structure:

#### 4A. Current Organizational Analysis
- **Team Structure Assessment**: Analyze current team structure and communication patterns
- **Communication Flow Mapping**: Map current communication flows and collaboration patterns
- **Skill Distribution**: Understand skill distribution across teams and individuals
- **Organizational Constraints**: Identify organizational constraints and change capacity

#### 4B. Target Team Topology Design
- **Stream-Aligned Teams**: Design teams aligned with value streams and customer outcomes
- **Enabling Teams**: Plan enabling teams for platform capabilities and specialist knowledge
- **Complicated Subsystem Teams**: Identify teams for complex technical subsystems
- **Platform Teams**: Design platform teams for shared infrastructure and capabilities

#### 4C. Team Interaction Patterns
- **Collaboration Patterns**: Define collaboration patterns between teams
- **X-as-a-Service Patterns**: Plan service provision patterns between teams
- **Facilitating Patterns**: Design facilitation and knowledge sharing patterns
- **Communication Protocols**: Establish communication protocols and interfaces

### 5. Service Catalog and Ownership Model

Define comprehensive service catalog with clear ownership:

#### 5A. Service Classification
- **Core Business Services**: Services that implement core business capabilities
- **Supporting Services**: Services that support core business functionality
- **Infrastructure Services**: Services that provide technical infrastructure capabilities
- **Integration Services**: Services that handle external system integration

#### 5B. Service Ownership Definition
- **Service Owner Identification**: Assign clear service ownership to teams
- **Responsibility Matrix**: Define responsibilities for development, operation, and evolution
- **Decision Rights**: Establish decision-making authority for service changes
- **Accountability Framework**: Create accountability framework for service outcomes

#### 5C. Service Lifecycle Management
- **Service Evolution Strategy**: Plan service evolution and versioning approaches
- **Deprecation Policies**: Establish service deprecation and retirement policies
- **Dependency Management**: Plan dependency management and change coordination
- **Service Registry**: Design service registry and discovery mechanisms

### 6. Technology and Infrastructure Implications

Consider technology implications of service decomposition:

#### 6A. Technology Stack Assessment
- **Polyglot Persistence**: Evaluate database technology choices for each service
- **Programming Language Selection**: Consider programming language choices and constraints
- **Framework and Library Decisions**: Assess framework choices and standardization needs
- **Technology Diversity Management**: Balance innovation with operational complexity

#### 6B. Infrastructure Requirements
- **Container Orchestration**: Plan Kubernetes deployment and management strategies
- **Service Mesh Integration**: Consider service mesh requirements and implementation
- **API Gateway Strategy**: Plan API gateway and traffic management approaches
- **Monitoring and Observability**: Design monitoring and observability infrastructure

#### 6C. Data Architecture Implications
- **Database per Service**: Plan database isolation and data ownership strategies
- **Data Synchronization**: Design data synchronization and consistency mechanisms
- **Event-Driven Architecture**: Plan event streaming and message broker requirements
- **Data Pipeline Architecture**: Consider data pipeline and analytics requirements

### 7. Risk Assessment and Mitigation

Identify and mitigate risks associated with service decomposition:

#### 7A. Technical Risks
- **Distributed System Complexity**: Assess complexity of distributed system management
- **Network Latency and Reliability**: Consider network-related performance risks
- **Data Consistency Challenges**: Evaluate eventual consistency and transaction risks
- **Service Discovery and Configuration**: Assess service discovery and configuration complexity

#### 7B. Organizational Risks
- **Team Coordination Overhead**: Evaluate coordination overhead between teams
- **Skill Gap Risks**: Identify skill gaps and training requirements
- **Change Management Challenges**: Assess organizational change management needs
- **Communication and Collaboration**: Consider communication and collaboration challenges

#### 7C. Business Risks
- **Delivery Timeline Impact**: Assess impact on delivery timelines and milestones
- **Quality and Reliability Risks**: Consider quality and reliability implications
- **Cost and Resource Implications**: Evaluate cost and resource requirements
- **Customer Experience Impact**: Assess potential impact on customer experience

### 8. Implementation Roadmap and Migration Strategy

Plan phased implementation and migration approach:

#### 8A. Migration Strategy
- **Strangler Fig Pattern**: Plan gradual migration using strangler fig approach
- **Database Decomposition**: Plan database decomposition and data migration
- **Service Extraction**: Design service extraction and refactoring strategies
- **Legacy System Integration**: Plan integration with existing legacy systems

#### 8B. Implementation Phases
- **Phase 1 - Foundation**: Establish platform infrastructure and core services
- **Phase 2 - Core Services**: Implement core business services and capabilities
- **Phase 3 - Integration**: Implement service integration and communication patterns
- **Phase 4 - Optimization**: Optimize performance, scaling, and operational excellence

#### 8C. Success Metrics and Validation
- **Business Metrics**: Define business value and outcome metrics
- **Technical Metrics**: Establish technical performance and quality metrics
- **Team Metrics**: Define team productivity and satisfaction metrics
- **Operational Metrics**: Plan operational excellence and reliability metrics

## Deliverables

### Primary Deliverable
Comprehensive Service Decomposition Analysis including:
- Business capability map with service boundary recommendations
- Domain model with bounded contexts and aggregate definitions
- Service catalog with ownership and responsibility assignments
- Team topology recommendations with interaction patterns
- Technology implications and infrastructure requirements
- Risk assessment with mitigation strategies
- Implementation roadmap with migration strategy

### Secondary Deliverables
- Service boundary decision records with rationale
- Team topology design with communication patterns
- Technology stack recommendations with trade-off analysis
- Risk register with mitigation plans
- Implementation timeline with milestones and dependencies

## Success Criteria

- Service boundaries align with business capabilities and domain boundaries
- Team topology supports effective service ownership and operation
- Service sizing enables team autonomy while minimizing coordination overhead
- Technology choices support service independence and operational excellence
- Implementation roadmap provides clear path from current state to target architecture
- Risk mitigation strategies address identified technical and organizational challenges

## Validation Checklist

- [ ] Business capabilities mapped to service boundaries with clear rationale
- [ ] Domain models defined with bounded contexts and aggregate boundaries
- [ ] Service sizing appropriate for team cognitive load and operational complexity
- [ ] Team topology aligned with Conway's Law and organizational constraints
- [ ] Technology implications assessed with infrastructure requirements defined
- [ ] Risk assessment comprehensive with mitigation strategies identified
- [ ] Implementation roadmap realistic with clear phases and milestones
- [ ] Service ownership model clear with accountability framework established
