Okay, I can help with that! To best guide you, I need to know a little more about what you're trying to accomplish.

First, here's a list of the specialist agents available and their primary functions:

1.  **<PERSON>lyst (<PERSON>)**: "Project Analyst and Brainstorming Coach"[cite: 3].
    * **Description**: Good for initial project analysis and brainstorming.
    * **Tasks**:
        * Brain Storming
        * Deep Research
        * Project Briefing
2.  **Product Manager (<PERSON>)**: "For PRDs, project planning, PM checklists and potential replans." [cite: 4]
    * **Description**: Use for creating Product Requirements Documents (PRDs), project planning, and navigating changes.
    * **Tasks**:
        * Create Prd
        * Correct Course
        * Create Deep Research Prompt
3.  **Architect (Fred)**: "For system architecture, technical design, architecture checklists." [cite: 5]
    * **Description**: Handles system architecture and technical design.
    * **Tasks**:
        * Create Architecture
        * Create Deep Research Prompt
4.  **Design Architect (<PERSON>)**: "For UI/UX specifications, front-end architecture." [cite: 6]
    * **Description**: Specializes in UI/UX specifications and front-end architecture.
    * **Tasks**:
        * Create Frontend Architecture
        * Create Ai Frontend Prompt
        * Create UX/UI Spec
5.  **PO (<PERSON>)**: "Product Owner"[cite: 6].
    * **Description**: Manages the product backlog, validates documents, and helps with story creation and adjustments.
    * **Tasks**:
        * Run Pm Checklist [from `checklists#pm-checklist` for Product Manager John, PO Sarah has `checklists#po-master-checklist`]
        * Run Po Master Checklist
        * Run Change Checklist
        * Extracts Epics and shards the Architecture
        * Correct Course
6.  **SM (Bob)**: "A very Technical Scrum Master helps the team run the Scrum process." [cite: 7, 8]
    * **Description**: A technical Scrum Master to help run the Scrum process, including story drafting and course correction.
    * **Tasks**:
        * Run Change Checklist
        * Run Story Dod Checklist
        * Run Story Draft Checklist
        * Correct Course
        * Draft a story for dev agent

To help me recommend the right agent, could you please tell me:

* What specific task are you trying to achieve right now?
* What is the current stage of your project? (e.g., idea phase, planning, design, dealing with a change)