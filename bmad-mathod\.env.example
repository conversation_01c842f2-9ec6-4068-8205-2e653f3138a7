# BMAD Method Environment Configuration Template
# Copy this file to .env and configure for your environment

# Build Configuration
NODE_ENV=development
BMAD_BUILD_DIR=./build
BMAD_ASSET_ROOT=./bmad-agent

# Agent Configuration
BMAD_ORCHESTRATOR_PROMPT=./bmad-agent/web-bmad-orchestrator-agent.md
BMAD_AGENT_CONFIG=./bmad-agent/web-bmad-orchestrator-agent.cfg.md

# Validation Settings
BMAD_VALIDATE_REFERENCES=true
BMAD_SKIP_IDE_FILES=true
BMAD_STRICT_MODE=false

# Deployment Settings
BMAD_DEPLOY_TARGET=web
BMAD_VERSION=4.0.0

# Optional: Custom Paths
# BMAD_CUSTOM_PERSONAS_PATH=
# BMAD_CUSTOM_TASKS_PATH=
# BMAD_CUSTOM_TEMPLATES_PATH=
# BMAD_CUSTOM_CHECKLISTS_PATH=

# Optional: Build Optimization
# BMAD_MINIFY_OUTPUT=false
# BMAD_INCLUDE_METADATA=true
# BMAD_BUNDLE_SIZE_LIMIT=10MB

# Optional: Logging
# BMAD_LOG_LEVEL=info
# BMAD_LOG_FILE=bmad-build.log

# Optional: Security
# BMAD_SECURE_MODE=false
# BMAD_VALIDATE_CHECKSUMS=false

# Microfrontend Configuration
# BMAD_INCLUDE_MICROFRONTEND_ASSETS=true
# BMAD_MICROFRONTEND_MODE=false
# BMAD_DESIGN_SYSTEM_INTEGRATION=false

# Optional: Microfrontend-Specific Paths
# BMAD_MICROFRONTEND_TEMPLATES_PATH=
# BMAD_MICROFRONTEND_TASKS_PATH=
# BMAD_DESIGN_SYSTEM_PATH=
# BMAD_API_GATEWAY_CONFIG_PATH=
