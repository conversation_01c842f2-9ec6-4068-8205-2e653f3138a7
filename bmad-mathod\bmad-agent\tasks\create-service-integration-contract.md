# Service Integration Contract Creation Task
## Cross-Service Communication and Dependency Management

## Purpose

- Define clear communication contracts between microservices
- Establish API specifications and event schemas for service integration
- Manage cross-service dependencies and coordination requirements
- Ensure reliable and maintainable service-to-service communication

Remember as you follow the upcoming instructions:

- Your contracts form the foundation for reliable microservices communication
- Output will be used by development teams to implement service integrations
- Focus on clear specifications that prevent integration issues
- Ensure contracts support service evolution and backward compatibility

## Instructions

### 1. Determine Integration Context

Before contract creation, establish the integration context:

A. **Integration Type Assessment:**
   - **Synchronous Integration:** Direct API calls requiring immediate response
   - **Asynchronous Integration:** Event-driven communication with eventual consistency
   - **Hybrid Integration:** Combination of both synchronous and asynchronous patterns

B. **Service Relationship Analysis:**
   - **Provider Service:** Service that exposes functionality or publishes events
   - **Consumer Service:** Service that calls APIs or consumes events
   - **Bidirectional:** Services that both provide and consume from each other

C. **Business Context Understanding:**
   - **Business Process:** What business workflow requires this integration
   - **Data Flow:** What information needs to be shared between services
   - **Timing Requirements:** Real-time vs. eventual consistency requirements

### 2. Review Integration Requirements

Review all available inputs:
- Service PRDs for both provider and consumer services
- System architecture documentation
- Business process requirements
- Performance and reliability requirements
- Security and compliance constraints

### 3. Choose Contract Creation Approach

Select appropriate approach based on integration complexity:

A. **Simple API Contract:** Straightforward request-response integration
B. **Event-Driven Contract:** Asynchronous event publishing and consumption
C. **Complex Integration Contract:** Multiple communication patterns and dependencies
D. **Legacy Integration Contract:** Integration with existing legacy systems

### 4. Process Contract Sections

Work through the contract sections using the `service-integration-contract-tmpl` template:

#### 4A. Integration Overview and Communication Patterns
- **Integration Purpose:** Clear business justification for the integration
- **Communication Pattern:** Synchronous, asynchronous, or hybrid approach
- **Integration Type:** Data sharing, workflow coordination, event notification, or command execution
- **Service Relationship:** Provider-consumer dynamics and responsibilities

#### 4B. API Contract Specifications (for Synchronous Integration)
- **Endpoint Definitions:** Complete REST API specifications with HTTP methods
- **Request/Response Schemas:** JSON schemas with validation rules and examples
- **Error Handling Contract:** Standardized error response formats and codes
- **Authentication and Authorization:** Security requirements and access control

#### 4C. Event Contract Specifications (for Asynchronous Integration)
- **Event Schema Definitions:** Complete event structure with required and optional fields
- **Event Publishing Contract:** Topic/queue configuration and publishing guarantees
- **Event Consumption Contract:** Processing requirements and error handling
- **Event Evolution:** Versioning strategy and backward compatibility

### 5. Define Data Consistency and Transaction Boundaries

#### 5A. Consistency Model Selection
- **Strong Consistency:** Immediate consistency across services (use sparingly)
- **Eventual Consistency:** Consistency achieved over time (preferred for microservices)
- **Causal Consistency:** Causally related operations maintain consistency

#### 5B. Transaction Coordination Strategy
- **No Distributed Transactions:** Each service manages its own transactions (preferred)
- **Saga Pattern:** Distributed transaction coordination with compensation actions
- **Two-Phase Commit:** Traditional distributed transactions (avoid if possible)

#### 5C. Compensation and Rollback Procedures
- **Compensation Actions:** Define rollback procedures for failed distributed transactions
- **Idempotency Requirements:** Ensure operations can be safely retried
- **Error Recovery:** Procedures for handling partial failures

### 6. Performance and Reliability Requirements

#### 6A. Performance Targets
- **Response Time:** Target latency for synchronous calls (e.g., < 200ms for 95th percentile)
- **Throughput:** Expected requests per second or events per minute
- **Availability:** Target uptime and reliability requirements (e.g., 99.9%)

#### 6B. Reliability Patterns
- **Circuit Breaker:** Fail fast when downstream service is unavailable
- **Retry with Backoff:** Retry failed requests with exponential backoff
- **Timeout Configuration:** Maximum wait time for responses
- **Bulkhead Pattern:** Resource isolation to prevent cascade failures

#### 6C. Fallback Strategies
- **Graceful Degradation:** How service continues with limited functionality
- **Default Values:** Default responses when service is unavailable
- **Cached Responses:** Use of cached data during outages

### 7. Security and Compliance Requirements

#### 7A. Authentication and Authorization
- **Service-to-Service Authentication:** JWT tokens, mTLS, or API keys
- **Authorization Rules:** What operations each service is authorized to perform
- **Token Management:** Token lifecycle and refresh procedures

#### 7B. Data Protection and Privacy
- **Encryption Requirements:** TLS for transit, encryption for sensitive data at rest
- **Data Masking:** PII and sensitive data protection
- **Audit Logging:** Security event logging and compliance tracking
- **Data Retention:** Data lifecycle and deletion policies

### 8. Monitoring and Observability

#### 8A. Metrics and Monitoring
- **Key Metrics:** Request count, response time, error rate, throughput, availability
- **Distributed Tracing:** Trace ID propagation and span creation
- **Business Metrics:** Domain-specific metrics for business monitoring

#### 8B. Alerting and Incident Response
- **Alerting Rules:** Thresholds for error rates, latency, and availability
- **Escalation Procedures:** Who to contact when integration fails
- **Troubleshooting Guides:** Common issues and resolution procedures

### 9. Testing and Validation Strategy

#### 9A. Contract Testing
- **Consumer-Driven Contracts:** Consumer defines expected contract behavior
- **Provider Contract Tests:** Provider validates contract compliance
- **Contract Evolution Tests:** Backward compatibility validation

#### 9B. Integration Testing
- **End-to-End Tests:** Full workflow testing across service boundaries
- **Component Tests:** Service boundary and integration point testing
- **Chaos Testing:** Failure scenario and resilience testing

#### 9C. Performance Testing
- **Load Testing:** Validate performance under expected load
- **Stress Testing:** Test behavior under extreme conditions
- **Latency Testing:** Measure end-to-end processing time

### 10. Versioning and Evolution Strategy

#### 10A. API Versioning
- **Versioning Strategy:** URL versioning, header versioning, or content negotiation
- **Backward Compatibility:** How to maintain compatibility during evolution
- **Deprecation Policy:** Timeline and procedures for deprecating old versions

#### 10B. Schema Evolution
- **Additive Changes:** Adding new optional fields (non-breaking)
- **Breaking Changes:** Field removal or type changes (requires version bump)
- **Migration Strategy:** How consumers migrate to new versions

### 11. Operational Procedures

#### 11A. Deployment Coordination
- **Deployment Order:** Which service should be deployed first
- **Rollback Procedures:** How to rollback if integration fails
- **Zero-Downtime Deployment:** Blue-green or canary deployment strategies

#### 11B. Incident Management
- **Communication Protocol:** How teams communicate during incidents
- **Escalation Path:** Clear escalation procedures for integration issues
- **Post-Incident Review:** Learning and improvement procedures

### 12. Contract Review and Approval

#### 12A. Technical Review
- **Provider Team Review:** Technical validation by the provider service team
- **Consumer Team Review:** Requirements validation by the consumer service team
- **Architecture Review:** System-wide architecture and pattern compliance

#### 12B. Business and Operational Review
- **Product Owner Approval:** Business value and requirement alignment
- **DevOps/SRE Review:** Operational feasibility and monitoring requirements
- **Security Review:** Security controls and compliance validation

## Deliverables

### Primary Deliverable
Complete Service Integration Contract following the `service-integration-contract-tmpl` template with:
- Clear integration specifications and communication patterns
- Comprehensive API or event contract definitions
- Performance, reliability, and security requirements
- Testing strategy and validation procedures
- Operational procedures and monitoring requirements

### Secondary Deliverables
- Event schema definitions (if applicable) using `event-schema-definition-tmpl`
- Integration testing strategy and test cases
- Monitoring and alerting configuration requirements
- Documentation for development teams

## Success Criteria

- Contract provides clear and unambiguous integration specifications
- All communication patterns and data formats are well-defined
- Performance and reliability requirements are realistic and measurable
- Security and compliance requirements are properly addressed
- Testing strategy ensures integration reliability and maintainability
- Operational procedures support reliable service communication
- Contract supports service evolution and backward compatibility

## Validation Checklist

- [ ] Integration purpose and business value clearly defined
- [ ] Communication patterns appropriate for use case
- [ ] API or event contracts are complete and well-specified
- [ ] Error handling and fallback strategies are defined
- [ ] Performance and reliability requirements are measurable
- [ ] Security requirements are comprehensive and implementable
- [ ] Testing strategy covers functional and non-functional requirements
- [ ] Monitoring and alerting requirements are specified
- [ ] Versioning and evolution strategy supports maintainability
- [ ] All stakeholders have reviewed and approved the contract
