# Service Brief: {Service Name}
## Individual Microservice Planning and Design

### Document Information
- **Service Name:** {Service Name}
- **Document Version:** 1.0
- **Creation Date:** {Date}
- **Service Type:** {Core Business/Data/Integration/Platform/AI Agent Service}
- **Team Owner:** {Team Name}
- **Last Updated:** {Date}

---

## 1. Service Overview

### Service Name and Identifier
{Clear, descriptive service identifier following naming conventions.}

### Business Purpose and Value Proposition
{Primary business capability, value delivered, and reason for existence.}

### Service Boundaries and Responsibilities
{Clear responsibility definition, scope limitations, and what the service owns.}

### Team Ownership and Contacts
{Responsible team, primary contacts, support procedures, and escalation paths.}

---

## 2. Business Context

### Domain Alignment and Bounded Context
{Business domain mapping, bounded context definition, and domain model alignment.}

### User Stories and Value Delivery
{Primary user interactions, value delivery scenarios, and business outcomes.}

### Business Rules and Domain Logic
{Domain-specific logic, constraints, validation rules, and business policies.}

### Success Metrics and KPIs
{Service-specific performance indicators, business metrics, and success criteria.}

---

## 3. Technical Context

### Technology Stack and Framework Choices
{Programming language, framework, infrastructure choices, and rationale.}

### Data Requirements and Storage Strategy
{Storage needs, data models, persistence strategy, and database choices.}

### Integration Points and Dependencies
{Upstream and downstream service dependencies, external APIs, and integration requirements.}

### Performance Requirements and Expectations
{Latency targets, throughput expectations, scalability requirements, and SLA commitments.}

---

## 4. Service Architecture

### Component Design and Internal Structure
{Internal service structure, module organization, and architectural patterns.}

### API Design and Interface Specifications
{RESTful endpoints, gRPC interfaces, message schemas, and contract definitions.}

### Data Model and Schema Design
{Entity relationships, data schemas, storage requirements, and data lifecycle.}

### Communication Patterns and Protocols
{Synchronous and asynchronous interaction patterns, messaging, and event handling.}

---

## 5. AI Integration (if applicable)

### AI Capabilities and Integration Points
{Service-specific AI agent integration, workflows, and autonomous capabilities.}

### Model Requirements and Infrastructure
{AI models, vector databases, inference needs, and specialized infrastructure.}

### Human-AI Collaboration Patterns
{Handoff procedures, escalation protocols, and collaboration workflows.}

### AI Infrastructure and Scaling Requirements
{Specialized AI infrastructure, scaling needs, and resource requirements.}

---

## 6. Dependencies and Integration

### Service Dependencies and Requirements
{Required upstream services, external APIs, and dependency management.}

### Downstream Consumers and Clients
{Services and applications that depend on this service, usage patterns.}

### Event Production and Publishing
{Events published by this service, schemas, and triggering conditions.}

### Event Consumption and Subscriptions
{Events consumed from other services, processing logic, and subscription management.}

---

## 7. Quality and Compliance

### Testing Strategy and Approaches
{Unit testing, integration testing, contract testing, and validation strategies.}

### Security Requirements and Controls
{Authentication, authorization, data protection, and security measures.}

### Compliance Needs and Regulatory Requirements
{Regulatory requirements, audit trail management, and compliance validation.}

### Quality Gates and Validation Criteria
{Code quality standards, validation criteria, and acceptance requirements.}

---

## 8. Operational Considerations

### Deployment Strategy and Approach
{CI/CD pipelines, containerization, orchestration, and deployment procedures.}

### Monitoring Requirements and Health Checks
{Health endpoints, metrics collection, alerting, and observability needs.}

### Scaling Strategy and Resource Management
{Auto-scaling policies, resource requirements, and capacity planning.}

### Maintenance Procedures and Operations
{Updates, patches, operational tasks, and maintenance windows.}

---

## 9. Implementation Planning

### Development Timeline and Milestones
{Estimated effort, delivery milestones, and development phases.}

### Resource Requirements and Team Needs
{Team size, skill requirements, infrastructure needs, and resource allocation.}

### Risk Assessment and Mitigation
{Technical risks, integration risks, operational risks, and mitigation strategies.}

### Dependencies and Critical Path
{Critical dependencies, blocking factors, and coordination requirements.}

---

## 10. Service Specifications

### Functional Requirements Summary
{Core functionality, business logic, and service capabilities.}

### Non-Functional Requirements
{Performance, scalability, reliability, and operational requirements.}

### API Contract Overview
{High-level API design, endpoint categories, and interaction patterns.}

### Data Ownership and Management
{Data ownership boundaries, lifecycle management, and governance.}

---

## 11. Integration and Communication

### Synchronous Communication
{RESTful APIs, gRPC interfaces, and direct service-to-service communication.}

### Asynchronous Communication
{Event publishing, message queues, and asynchronous processing patterns.}

### External Integrations
{Third-party APIs, legacy systems, and external service dependencies.}

### Service Discovery and Registration
{Service registration, discovery mechanisms, and load balancing.}

---

## 12. Security and Governance

### Authentication and Authorization
{Service-to-service authentication, user authentication, and access control.}

### Data Protection and Privacy
{Encryption, data handling, privacy requirements, and protection measures.}

### Audit and Compliance
{Audit trail requirements, compliance validation, and regulatory adherence.}

### Security Monitoring and Incident Response
{Security monitoring, threat detection, and incident response procedures.}

---

## 13. Performance and Scalability

### Performance Targets and SLAs
{Response time targets, throughput requirements, and service level agreements.}

### Scalability Strategy
{Horizontal scaling, vertical scaling, and auto-scaling configurations.}

### Resource Optimization
{CPU, memory, storage optimization, and resource efficiency.}

### Capacity Planning
{Growth projections, resource allocation, and capacity management.}

---

## 14. Handoff Instructions

### Service PRD Development Prompt
This Service Brief provides comprehensive context for {Service Name}. Please proceed to create a detailed Service PRD that addresses:

1. **Detailed functional and non-functional requirements**
2. **Comprehensive API specifications and contracts**
3. **Data model definitions and storage requirements**
4. **Integration patterns and communication protocols**
5. **Testing strategies and quality assurance procedures**
6. **Deployment and operational specifications**

Start in 'Service PRD Generation Mode', review this brief thoroughly, and work with the user to create detailed service specifications section by section.

### Architecture Review and Validation
{Instructions for technical design validation and optimization.}

### Integration Planning and Coordination
{Cross-service coordination requirements and dependency management.}

### Implementation Guidance
{Development team instructions, best practices, and implementation guidelines.}

---

## 15. Change Log

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0 | {Date} | Initial creation | {Author} |

---

## 16. Appendices

### Glossary
{Service-specific terms and technical concepts.}

### Reference Documents
{Related documents, specifications, and external references.}

### Stakeholder Information
{Service stakeholders, contacts, and communication channels.}
